import React, { useState } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Download, 
  MoreVertical, 
  Eye, 
  MessageCircle,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  UserCheck,
  UserX,
  Mail
} from 'lucide-react';

interface Participant {
  id: string;
  name: string;
  email: string;
  avatar: string;
  accuracy: number;
  avgTime: number;
  pollsCompleted: number;
  streak: number;
  lastActive: string;
  status: 'online' | 'offline' | 'away';
  joinedAt: string;
}

const ParticipantsManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('accuracy');
  const [filterStatus, setFilterStatus] = useState('all');

  const [participants] = useState<Participant[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'DP',
      accuracy: 95.1,
      avgTime: 1.8,
      pollsCompleted: 41,
      streak: 15,
      lastActive: '10 minutes ago',
      status: 'online',
      joinedAt: '2025-01-10'
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: '<PERSON>',
      accuracy: 92.5,
      avgTime: 2.1,
      pollsCompleted: 48,
      streak: 12,
      lastActive: '2 minutes ago',
      status: 'online',
      joinedAt: '2025-01-08'
    },
    {
      id: '3',
      name: 'Bob Smith',
      email: '<EMAIL>',
      avatar: 'BS',
      accuracy: 87.3,
      avgTime: 3.2,
      pollsCompleted: 35,
      streak: 8,
      lastActive: '5 minutes ago',
      status: 'online',
      joinedAt: '2025-01-12'
    },
    {
      id: '4',
      name: 'Ethan Hunt',
      email: '<EMAIL>',
      avatar: 'EH',
      accuracy: 83.6,
      avgTime: 2.9,
      pollsCompleted: 39,
      streak: 3,
      lastActive: '3 hours ago',
      status: 'away',
      joinedAt: '2025-01-05'
    },
    {
      id: '5',
      name: 'Charlie Brown',
      email: '<EMAIL>',
      avatar: 'CB',
      accuracy: 78.9,
      avgTime: 4.1,
      pollsCompleted: 52,
      streak: 5,
      lastActive: '1 hour ago',
      status: 'offline',
      joinedAt: '2025-01-15'
    }
  ]);

  const [joiningParticipants] = useState([
    { id: 'p1', name: 'Priya Sharma', email: '<EMAIL>', avatar: 'PS' },
    { id: 'p2', name: 'Rahul Verma', email: '<EMAIL>', avatar: 'RV' },
    { id: 'p3', name: 'Sara Lee', email: '<EMAIL>', avatar: 'SL' },
    { id: 'p4', name: 'Mohit Singh', email: '<EMAIL>', avatar: 'MS' },
    { id: 'p5', name: 'Emily Chen', email: '<EMAIL>', avatar: 'EC' }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <UserCheck className="h-4 w-4 text-green-400" />;
      case 'away': return <Clock className="h-4 w-4 text-yellow-400" />;
      case 'offline': return <UserX className="h-4 w-4 text-gray-400" />;
      default: return <UserX className="h-4 w-4 text-gray-400" />;
    }
  };

  const filteredParticipants = participants
    .filter(p => 
      p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(p => filterStatus === 'all' || p.status === filterStatus)
    .sort((a, b) => {
      switch (sortBy) {
        case 'accuracy': return b.accuracy - a.accuracy;
        case 'avgTime': return a.avgTime - b.avgTime;
        case 'polls': return b.pollsCompleted - a.pollsCompleted;
        case 'streak': return b.streak - a.streak;
        case 'name': return a.name.localeCompare(b.name);
        default: return 0;
      }
    });

  const onlineCount = participants.filter(p => p.status === 'online').length;
  const totalCount = participants.length;
  const avgAccuracy = participants.reduce((acc, p) => acc + p.accuracy, 0) / participants.length;
  const avgResponseTime = participants.reduce((acc, p) => acc + p.avgTime, 0) / participants.length;
  const totalPolls = participants.reduce((acc, p) => acc + p.pollsCompleted, 0);
  const topStreak = Math.max(...participants.map(p => p.streak));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/50 to-cyan-900/50 backdrop-blur-lg rounded-xl p-6 border border-blue-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <Users className="h-6 w-6 mr-3 text-blue-400" />
          Participants Management
        </h3>
        <p className="text-blue-200">Manage and monitor participant performance</p>
      </div>

      {/* Students Joining Section */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-xl font-semibold text-white">Students Joining This Poll</h4>
          <span className="bg-green-600 text-green-100 px-3 py-1 rounded-full text-sm font-medium">
            {joiningParticipants.length} Joined
          </span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {joiningParticipants.map((participant) => (
            <div key={participant.id} className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4 text-center hover:bg-purple-600/30 transition-all">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-white font-semibold text-sm">{participant.avatar}</span>
              </div>
              <h5 className="text-white font-medium text-sm mb-1">{participant.name}</h5>
              <p className="text-gray-400 text-xs">{participant.email}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <UserCheck className="h-5 w-5 text-green-400" />
            <span className="text-green-300 text-sm font-medium">{onlineCount} Online</span>
          </div>
          <p className="text-white text-lg font-bold">{totalCount} Total</p>
        </div>

        <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Target className="h-5 w-5 text-blue-400" />
            <span className="text-blue-300 text-sm font-medium">Average Accuracy</span>
          </div>
          <p className="text-white text-lg font-bold">{avgAccuracy.toFixed(1)}%</p>
        </div>

        <div className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Clock className="h-5 w-5 text-purple-400" />
            <span className="text-purple-300 text-sm font-medium">Avg Response Time</span>
          </div>
          <p className="text-white text-lg font-bold">{avgResponseTime.toFixed(1)}s</p>
        </div>

        <div className="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <TrendingUp className="h-5 w-5 text-yellow-400" />
            <span className="text-yellow-300 text-sm font-medium">Total Polls</span>
          </div>
          <p className="text-white text-lg font-bold">{totalPolls}</p>
        </div>

        <div className="bg-orange-600/20 border border-orange-500/30 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Trophy className="h-5 w-5 text-orange-400" />
            <span className="text-orange-300 text-sm font-medium">Top Streak</span>
          </div>
          <p className="text-white text-lg font-bold">{topStreak}</p>
        </div>

        <div className="bg-red-600/20 border border-red-500/30 rounded-lg p-4 text-center">
          <button className="w-full flex items-center justify-center space-x-2 text-red-300 hover:text-red-200 transition-colors">
            <Download className="h-5 w-5" />
            <span className="text-sm font-medium">Export All</span>
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search participants..."
              className="w-full pl-10 pr-4 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="accuracy">Sort by Accuracy</option>
              <option value="avgTime">Sort by Response Time</option>
              <option value="polls">Sort by Polls Completed</option>
              <option value="streak">Sort by Streak</option>
              <option value="name">Sort by Name</option>
            </select>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">All Status</option>
              <option value="online">Online</option>
              <option value="away">Away</option>
              <option value="offline">Offline</option>
            </select>
          </div>
        </div>
      </div>

      {/* Participants Table */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl border border-purple-500/30 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-purple-600/20 border-b border-purple-500/30">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Participant</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Accuracy</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Avg Time</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Polls</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Streak</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Last Active</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-purple-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {filteredParticipants.map((participant) => (
                <tr key={participant.id} className="hover:bg-purple-600/10 transition-colors">
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold text-sm">{participant.avatar}</span>
                        </div>
                        <div className={`absolute -bottom-1 -right-1 w-3 h-3 ${getStatusColor(participant.status)} rounded-full border-2 border-black`}></div>
                      </div>
                      <div>
                        <p className="text-white font-medium">{participant.name}</p>
                        <p className="text-gray-400 text-sm">{participant.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-semibold ${
                        participant.accuracy >= 90 ? 'text-green-400' :
                        participant.accuracy >= 80 ? 'text-yellow-400' :
                        'text-red-400'
                      }`}>
                        {participant.accuracy}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="text-blue-400 font-medium">{participant.avgTime}s</span>
                  </td>
                  <td className="px-6 py-4">
                    <span className="text-white">{participant.pollsCompleted}</span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-1">
                      <Trophy className="h-4 w-4 text-yellow-400" />
                      <span className="text-yellow-400 font-medium">{participant.streak}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(participant.status)}
                      <span className="text-gray-300 text-sm">{participant.lastActive}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-400 hover:text-blue-300 transition-colors" title="View Details">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-green-400 hover:text-green-300 transition-colors" title="Send Message">
                        <MessageCircle className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-purple-400 hover:text-purple-300 transition-colors" title="Send Email">
                        <Mail className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-300 transition-colors" title="More Options">
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-gray-400 text-sm">
          Showing {filteredParticipants.length} of {participants.length} participants
        </p>
        <div className="flex items-center space-x-2">
          <button className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors">
            Previous
          </button>
          <span className="px-3 py-1 bg-purple-600 text-white rounded text-sm">1</span>
          <button className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors">
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default ParticipantsManagement;
import React, { useState } from 'react';
import { 
  Globe, 
  Orbit, 
  Atom, 
  Zap, 
  Activity, 
  TrendingUp,
  Users,
  Target,
  Brain,
  Sparkles,
  BarChart3,
  Clock,
  Trophy,
  Star,
  BookOpen,
  Award,
  Flame
} from 'lucide-react';

const StudentDashboardViews: React.FC = () => {
  const [selectedView, setSelectedView] = useState<'classic' | 'planetary' | 'atomic'>('classic');

  const renderClassicView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Polls Participated */}
      <div className="bg-gradient-to-br from-blue-600/20 to-cyan-600/20 backdrop-blur-lg rounded-xl p-6 border border-blue-500/30">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
            <Target className="h-6 w-6 text-white" />
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-white">24</p>
            <p className="text-sm text-blue-200">Polls Participated</p>
          </div>
        </div>
        <div className="w-full bg-blue-900/30 rounded-full h-2">
          <div className="bg-blue-400 h-2 rounded-full animate-pulse" style={{ width: '80%' }}></div>
        </div>
        <p className="text-xs text-blue-300 mt-2">+3 this week</p>
      </div>

      {/* Accuracy Rate */}
      <div className="bg-gradient-to-br from-green-600/20 to-emerald-600/20 backdrop-blur-lg rounded-xl p-6 border border-green-500/30">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
            <Trophy className="h-6 w-6 text-white" />
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-white">87%</p>
            <p className="text-sm text-green-200">Accuracy Rate</p>
          </div>
        </div>
        <div className="w-full bg-green-900/30 rounded-full h-2">
          <div className="bg-green-400 h-2 rounded-full animate-pulse" style={{ width: '87%' }}></div>
        </div>
        <p className="text-xs text-green-300 mt-2">+5% improvement</p>
      </div>

      {/* Current Streak */}
      <div className="bg-gradient-to-br from-orange-600/20 to-red-600/20 backdrop-blur-lg rounded-xl p-6 border border-orange-500/30">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <Flame className="h-6 w-6 text-white" />
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-white">12</p>
            <p className="text-sm text-orange-200">Day Streak</p>
          </div>
        </div>
        <div className="w-full bg-orange-900/30 rounded-full h-2">
          <div className="bg-orange-400 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
        </div>
        <p className="text-xs text-orange-300 mt-2">Keep it up!</p>
      </div>

      {/* Current Rank */}
      <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
            <Award className="h-6 w-6 text-white" />
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-white">#5</p>
            <p className="text-sm text-purple-200">Current Rank</p>
          </div>
        </div>
        <div className="w-full bg-purple-900/30 rounded-full h-2">
          <div className="bg-purple-400 h-2 rounded-full animate-pulse" style={{ width: '85%' }}></div>
        </div>
        <p className="text-xs text-purple-300 mt-2">↑2 positions</p>
      </div>
    </div>
  );

  const renderPlanetaryView = () => (
    <div className="relative w-full h-96 flex items-center justify-center">
      {/* Central Student Avatar */}
      <div className="absolute w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center animate-pulse z-10">
        <Users className="h-12 w-12 text-white" />
        <div className="absolute inset-0 rounded-full bg-blue-400/20 animate-ping"></div>
      </div>

      {/* Orbiting Achievement Elements */}
      <div className="absolute inset-0 animate-spin" style={{ animationDuration: '20s' }}>
        {/* Accuracy Orbit */}
        <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-green-500/80 rounded-full flex items-center justify-center backdrop-blur-lg border border-green-400/50">
          <Trophy className="h-8 w-8 text-white" />
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-green-300 whitespace-nowrap">
            87% Accuracy
          </div>
        </div>
        
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-blue-500/80 rounded-full flex items-center justify-center backdrop-blur-lg border border-blue-400/50">
          <Target className="h-8 w-8 text-white" />
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-blue-300 whitespace-nowrap">
            24 Polls
          </div>
        </div>
        
        <div className="absolute left-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-orange-500/80 rounded-full flex items-center justify-center backdrop-blur-lg border border-orange-400/50">
          <Flame className="h-8 w-8 text-white" />
          <div className="absolute -right-12 top-1/2 transform -translate-y-1/2 text-xs text-orange-300 whitespace-nowrap">
            12 Day Streak
          </div>
        </div>
        
        <div className="absolute right-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-purple-500/80 rounded-full flex items-center justify-center backdrop-blur-lg border border-purple-400/50">
          <Award className="h-8 w-8 text-white" />
          <div className="absolute -left-12 top-1/2 transform -translate-y-1/2 text-xs text-purple-300 whitespace-nowrap">
            Rank #5
          </div>
        </div>
      </div>

      {/* Second Orbit - Study Materials */}
      <div className="absolute inset-8 animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-cyan-400 rounded-full animate-bounce flex items-center justify-center">
          <BookOpen className="h-4 w-4 text-white" />
        </div>
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-pink-400 rounded-full animate-bounce flex items-center justify-center">
          <Brain className="h-4 w-4 text-white" />
        </div>
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-emerald-400 rounded-full animate-bounce flex items-center justify-center">
          <Star className="h-4 w-4 text-white" />
        </div>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-yellow-400 rounded-full animate-bounce flex items-center justify-center">
          <Zap className="h-4 w-4 text-white" />
        </div>
      </div>

      {/* Data Streams */}
      <div className="absolute inset-16 animate-spin" style={{ animationDuration: '10s' }}>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-indigo-400 rounded-full animate-pulse"></div>
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-rose-400 rounded-full animate-pulse"></div>
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-teal-400 rounded-full animate-pulse"></div>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-amber-400 rounded-full animate-pulse"></div>
      </div>
    </div>
  );

  const renderAtomicView = () => (
    <div className="relative w-full h-96 flex items-center justify-center">
      {/* Nucleus - Student Core */}
      <div className="absolute w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center z-10">
        <Users className="h-10 w-10 text-white animate-spin" style={{ animationDuration: '3s' }} />
      </div>

      {/* Electron Orbits - Skills */}
      <div className="absolute w-64 h-64 border-2 border-green-400/30 rounded-full animate-spin" style={{ animationDuration: '8s' }}>
        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-400 rounded-full animate-pulse flex items-center justify-center">
          <Trophy className="h-2 w-2 text-white" />
        </div>
        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-400 rounded-full animate-pulse flex items-center justify-center">
          <Target className="h-2 w-2 text-white" />
        </div>
      </div>

      <div className="absolute w-80 h-80 border-2 border-blue-400/30 rounded-full animate-spin" style={{ animationDuration: '12s', animationDirection: 'reverse' }}>
        <div className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-400 rounded-full animate-pulse flex items-center justify-center">
          <BookOpen className="h-2 w-2 text-white" />
        </div>
        <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-400 rounded-full animate-pulse flex items-center justify-center">
          <Brain className="h-2 w-2 text-white" />
        </div>
      </div>

      <div className="absolute w-96 h-96 border-2 border-purple-400/30 rounded-full animate-spin" style={{ animationDuration: '16s' }}>
        <div className="absolute top-8 right-8 w-4 h-4 bg-purple-400 rounded-full animate-pulse flex items-center justify-center">
          <Star className="h-2 w-2 text-white" />
        </div>
        <div className="absolute bottom-8 left-8 w-4 h-4 bg-purple-400 rounded-full animate-pulse flex items-center justify-center">
          <Award className="h-2 w-2 text-white" />
        </div>
      </div>

      {/* Achievement Data Points */}
      <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-lg rounded-lg p-3 border border-green-400/30">
        <div className="text-green-400 text-xs font-medium">Accuracy</div>
        <div className="text-white text-lg font-bold">87%</div>
      </div>

      <div className="absolute top-4 right-4 bg-black/60 backdrop-blur-lg rounded-lg p-3 border border-blue-400/30">
        <div className="text-blue-400 text-xs font-medium">Polls</div>
        <div className="text-white text-lg font-bold">24</div>
      </div>

      <div className="absolute bottom-4 left-4 bg-black/60 backdrop-blur-lg rounded-lg p-3 border border-orange-400/30">
        <div className="text-orange-400 text-xs font-medium">Streak</div>
        <div className="text-white text-lg font-bold">12</div>
      </div>

      <div className="absolute bottom-4 right-4 bg-black/60 backdrop-blur-lg rounded-lg p-3 border border-purple-400/30">
        <div className="text-purple-400 text-xs font-medium">Rank</div>
        <div className="text-white text-lg font-bold">#5</div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Dashboard Type Selector */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <Globe className="h-5 w-5 mr-2 text-blue-400" />
          Student Dashboard Views
        </h3>
        
        <div className="grid grid-cols-3 gap-4">
          <button
            onClick={() => setSelectedView('classic')}
            className={`p-4 rounded-lg border transition-all ${
              selectedView === 'classic'
                ? 'bg-blue-600 border-blue-500 text-white'
                : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
            }`}
          >
            <BarChart3 className="h-6 w-6 mx-auto mb-2" />
            <span className="text-sm font-medium">Classic View</span>
          </button>
          
          <button
            onClick={() => setSelectedView('planetary')}
            className={`p-4 rounded-lg border transition-all ${
              selectedView === 'planetary'
                ? 'bg-purple-600 border-purple-500 text-white'
                : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
            }`}
          >
            <Orbit className="h-6 w-6 mx-auto mb-2" />
            <span className="text-sm font-medium">Planetary View</span>
          </button>
          
          <button
            onClick={() => setSelectedView('atomic')}
            className={`p-4 rounded-lg border transition-all ${
              selectedView === 'atomic'
                ? 'bg-green-600 border-green-500 text-white'
                : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
            }`}
          >
            <Atom className="h-6 w-6 mx-auto mb-2" />
            <span className="text-sm font-medium">Atomic View</span>
          </button>
        </div>
      </div>

      {/* Selected Dashboard View */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-8 border border-purple-500/30 min-h-[400px]">
        {selectedView === 'classic' && renderClassicView()}
        {selectedView === 'planetary' && renderPlanetaryView()}
        {selectedView === 'atomic' && renderAtomicView()}
      </div>

      {/* Quick Achievement Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-green-600/20 to-emerald-600/20 backdrop-blur-lg rounded-lg p-4 border border-green-500/30">
          <div className="flex items-center space-x-3">
            <Clock className="h-8 w-8 text-green-400" />
            <div>
              <p className="text-green-200 text-sm">Avg Response</p>
              <p className="text-white text-lg font-bold">1.8s</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 backdrop-blur-lg rounded-lg p-4 border border-blue-500/30">
          <div className="flex items-center space-x-3">
            <Trophy className="h-8 w-8 text-blue-400" />
            <div>
              <p className="text-blue-200 text-sm">Best Score</p>
              <p className="text-white text-lg font-bold">98%</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-lg rounded-lg p-4 border border-purple-500/30">
          <div className="flex items-center space-x-3">
            <Star className="h-8 w-8 text-purple-400" />
            <div>
              <p className="text-purple-200 text-sm">Total Points</p>
              <p className="text-white text-lg font-bold">2,847</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 backdrop-blur-lg rounded-lg p-4 border border-yellow-500/30">
          <div className="flex items-center space-x-3">
            <Zap className="h-8 w-8 text-yellow-400" />
            <div>
              <p className="text-yellow-200 text-sm">Study Hours</p>
              <p className="text-white text-lg font-bold">47h</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboardViews;
import { performance } from 'perf_hooks';
import { Request, Response, NextFunction } from 'express';

interface PerformanceMetric {
  endpoint: string;
  method: string;
  duration: number;
  timestamp: Date;
  statusCode: number;
  userAgent?: string;
  ip?: string;
}

interface DatabaseMetric {
  operation: string;
  collection: string;
  duration: number;
  timestamp: Date;
  success: boolean;
}

class PerformanceService {
  private metrics: PerformanceMetric[] = [];
  private dbMetrics: DatabaseMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics in memory

  // Request performance tracking middleware
  trackRequest() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = performance.now();
      
      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = function(this: Response, ...args: any[]) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Record metric
        const metric: PerformanceMetric = {
          endpoint: req.route?.path || req.path,
          method: req.method,
          duration,
          timestamp: new Date(),
          statusCode: res.statusCode,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        };
        
        performanceService.addMetric(metric);
        
        // Log slow requests (> 1 second)
        if (duration > 1000) {
          console.warn(`🐌 Slow request detected: ${req.method} ${req.path} - ${duration.toFixed(2)}ms`);
        }
        
        originalEnd.apply(this, args);
      };
      
      next();
    };
  }

  // Add performance metric
  addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the last N metrics to prevent memory issues
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  // Add database metric
  addDatabaseMetric(metric: DatabaseMetric): void {
    this.dbMetrics.push(metric);
    
    // Keep only the last N metrics
    if (this.dbMetrics.length > this.maxMetrics) {
      this.dbMetrics = this.dbMetrics.slice(-this.maxMetrics);
    }
  }

  // Database operation wrapper
  async trackDatabaseOperation<T>(
    operation: string,
    collection: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    let success = true;
    
    try {
      const result = await fn();
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.addDatabaseMetric({
        operation,
        collection,
        duration,
        timestamp: new Date(),
        success
      });
      
      // Log slow database operations (> 500ms)
      if (duration > 500) {
        console.warn(`🐌 Slow DB operation: ${operation} on ${collection} - ${duration.toFixed(2)}ms`);
      }
    }
  }

  // Get performance statistics
  getStats(timeRangeMinutes: number = 60): {
    requests: {
      total: number;
      averageResponseTime: number;
      slowRequests: number;
      errorRate: number;
      requestsPerMinute: number;
    };
    database: {
      total: number;
      averageResponseTime: number;
      slowOperations: number;
      errorRate: number;
    };
    endpoints: Array<{
      endpoint: string;
      method: string;
      count: number;
      averageResponseTime: number;
      errorRate: number;
    }>;
  } {
    const cutoffTime = new Date(Date.now() - timeRangeMinutes * 60 * 1000);
    
    // Filter metrics within time range
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    const recentDbMetrics = this.dbMetrics.filter(m => m.timestamp >= cutoffTime);
    
    // Request statistics
    const totalRequests = recentMetrics.length;
    const averageResponseTime = totalRequests > 0 
      ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
      : 0;
    const slowRequests = recentMetrics.filter(m => m.duration > 1000).length;
    const errorRequests = recentMetrics.filter(m => m.statusCode >= 400).length;
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
    const requestsPerMinute = totalRequests / timeRangeMinutes;
    
    // Database statistics
    const totalDbOps = recentDbMetrics.length;
    const averageDbResponseTime = totalDbOps > 0
      ? recentDbMetrics.reduce((sum, m) => sum + m.duration, 0) / totalDbOps
      : 0;
    const slowDbOps = recentDbMetrics.filter(m => m.duration > 500).length;
    const failedDbOps = recentDbMetrics.filter(m => !m.success).length;
    const dbErrorRate = totalDbOps > 0 ? (failedDbOps / totalDbOps) * 100 : 0;
    
    // Endpoint statistics
    const endpointStats = new Map<string, {
      count: number;
      totalTime: number;
      errors: number;
    }>();
    
    recentMetrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`;
      const existing = endpointStats.get(key) || { count: 0, totalTime: 0, errors: 0 };
      
      existing.count++;
      existing.totalTime += metric.duration;
      if (metric.statusCode >= 400) existing.errors++;
      
      endpointStats.set(key, existing);
    });
    
    const endpoints = Array.from(endpointStats.entries()).map(([key, stats]) => {
      const [method, endpoint] = key.split(' ', 2);
      return {
        endpoint,
        method,
        count: stats.count,
        averageResponseTime: stats.totalTime / stats.count,
        errorRate: (stats.errors / stats.count) * 100
      };
    }).sort((a, b) => b.count - a.count); // Sort by request count
    
    return {
      requests: {
        total: totalRequests,
        averageResponseTime,
        slowRequests,
        errorRate,
        requestsPerMinute
      },
      database: {
        total: totalDbOps,
        averageResponseTime: averageDbResponseTime,
        slowOperations: slowDbOps,
        errorRate: dbErrorRate
      },
      endpoints
    };
  }

  // Get system health metrics
  getSystemHealth(): {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    uptime: number;
    nodeVersion: string;
  } {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal;
    const usedMemory = memUsage.heapUsed;
    
    return {
      memory: {
        used: usedMemory,
        total: totalMemory,
        percentage: (usedMemory / totalMemory) * 100
      },
      uptime: process.uptime(),
      nodeVersion: process.version
    };
  }

  // Clear old metrics
  clearOldMetrics(olderThanHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    this.dbMetrics = this.dbMetrics.filter(m => m.timestamp >= cutoffTime);
    
    console.log(`🧹 Cleared performance metrics older than ${olderThanHours} hours`);
  }

  // Start periodic cleanup
  startPeriodicCleanup(): void {
    // Clean up old metrics every hour
    setInterval(() => {
      this.clearOldMetrics(24); // Keep 24 hours of data
    }, 60 * 60 * 1000); // Every hour
    
    console.log('📊 Performance monitoring started with periodic cleanup');
  }

  // Export metrics for external monitoring tools
  exportMetrics(): {
    requests: PerformanceMetric[];
    database: DatabaseMetric[];
  } {
    return {
      requests: [...this.metrics],
      database: [...this.dbMetrics]
    };
  }
}

// Create singleton instance
const performanceService = new PerformanceService();

export default performanceService;

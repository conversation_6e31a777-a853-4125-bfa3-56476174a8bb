import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import Poll from '../models/Poll.js';
import Session from '../models/Session.js';
import User from '../models/User.js';

export const getSessionAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId } = req.params;
  
  const session = await Session.findById(sessionId).populate('polls');
  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }
  
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to view analytics for this session' });
  }
  
  const polls = await Poll.find({ session: sessionId }).populate('responses.user', 'name email');
  
  // Calculate comprehensive analytics
  const analytics = {
    overview: {
      sessionTitle: session.title,
      totalPolls: polls.length,
      totalParticipants: session.participants.length,
      totalResponses: polls.reduce((sum, poll) => sum + poll.responses.length, 0),
      averageEngagement: session.analytics.averageEngagement,
      duration: session.endTime && session.startTime 
        ? Math.round((session.endTime.getTime() - session.startTime.getTime()) / 1000 / 60) 
        : null
    },
    participation: {
      peakParticipants: session.analytics.peakParticipants,
      averageParticipantsPerPoll: polls.length > 0 
        ? polls.reduce((sum, poll) => sum + poll.responses.length, 0) / polls.length 
        : 0,
      participationTrend: polls.map(poll => ({
        pollTitle: poll.title,
        responses: poll.responses.length,
        timestamp: poll.startTime
      }))
    },
    performance: {
      averageAccuracy: polls.length > 0 
        ? polls.reduce((sum, poll) => sum + poll.analytics.accuracyRate, 0) / polls.length 
        : 0,
      averageResponseTime: polls.length > 0 
        ? polls.reduce((sum, poll) => sum + poll.analytics.averageResponseTime, 0) / polls.length 
        : 0,
      difficultyBreakdown: {
        easy: polls.filter(p => p.difficulty === 'Easy').length,
        medium: polls.filter(p => p.difficulty === 'Medium').length,
        hard: polls.filter(p => p.difficulty === 'Hard').length
      }
    },
    topPerformers: await getTopPerformers(sessionId),
    pollBreakdown: polls.map(poll => ({
      _id: poll._id,
      title: poll.title,
      responses: poll.responses.length,
      accuracy: poll.analytics.accuracyRate,
      averageTime: poll.analytics.averageResponseTime,
      difficulty: poll.difficulty
    }))
  };
  
  res.json({
    success: true,
    analytics
  });
});

export const getUserAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { userId } = req.params;
  
  // Users can only view their own analytics unless they're admin/host
  if (req.user?._id.toString() !== userId && !['admin', 'host'].includes(req.user?.role || '')) {
    return res.status(403).json({ message: 'Access denied' });
  }
  
  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }
  
  // Get user's poll responses
  const userPolls = await Poll.find({
    'responses.user': userId
  }).select('title question responses analytics difficulty createdAt');
  
  const userResponses = userPolls.map(poll => {
    const userResponse = poll.responses.find(r => r.user.toString() === userId);
    return {
      pollId: poll._id,
      pollTitle: poll.title,
      isCorrect: userResponse?.isCorrect || false,
      responseTime: userResponse?.responseTime || 0,
      difficulty: poll.difficulty,
      submittedAt: userResponse?.submittedAt
    };
  });
  
  const analytics = {
    overview: {
      totalPolls: userResponses.length,
      correctAnswers: userResponses.filter(r => r.isCorrect).length,
      accuracy: userResponses.length > 0 
        ? (userResponses.filter(r => r.isCorrect).length / userResponses.length) * 100 
        : 0,
      averageResponseTime: userResponses.length > 0 
        ? userResponses.reduce((sum, r) => sum + r.responseTime, 0) / userResponses.length 
        : 0,
      totalPoints: user.stats.totalPoints,
      currentStreak: user.stats.currentStreak
    },
    performance: {
      byDifficulty: {
        easy: calculateDifficultyStats(userResponses, 'Easy'),
        medium: calculateDifficultyStats(userResponses, 'Medium'),
        hard: calculateDifficultyStats(userResponses, 'Hard')
      },
      recentTrend: userResponses
        .sort((a, b) => new Date(b.submittedAt!).getTime() - new Date(a.submittedAt!).getTime())
        .slice(0, 10)
        .map(r => ({
          date: r.submittedAt,
          isCorrect: r.isCorrect,
          responseTime: r.responseTime
        }))
    },
    achievements: user.achievements,
    ranking: await getUserRanking(userId)
  };
  
  res.json({
    success: true,
    analytics
  });
});

export const getPollAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { pollId } = req.params;
  
  const poll = await Poll.findById(pollId)
    .populate('responses.user', 'name email')
    .populate('creator', 'name email');
  
  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }
  
  if (poll.creator._id.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to view analytics for this poll' });
  }
  
  const analytics = {
    overview: {
      title: poll.title,
      totalResponses: poll.responses.length,
      averageResponseTime: poll.analytics.averageResponseTime,
      accuracyRate: poll.analytics.accuracyRate,
      difficulty: poll.difficulty
    },
    optionAnalysis: poll.options.map((option, index) => ({
      text: option.text,
      votes: option.votes,
      percentage: poll.totalVotes > 0 ? (option.votes / poll.totalVotes) * 100 : 0,
      isCorrect: poll.correctAnswer === index
    })),
    responseTimeDistribution: {
      fast: poll.responses.filter(r => r.responseTime < 5000).length,
      medium: poll.responses.filter(r => r.responseTime >= 5000 && r.responseTime < 15000).length,
      slow: poll.responses.filter(r => r.responseTime >= 15000).length
    },
    participantDetails: poll.responses.map(response => ({
      user: response.user,
      selectedOption: response.selectedOption,
      isCorrect: response.isCorrect,
      responseTime: response.responseTime,
      submittedAt: response.submittedAt
    }))
  };
  
  res.json({
    success: true,
    analytics
  });
});

export const getLeaderboard = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId } = req.params;
  const { limit = 10 } = req.query;
  
  const session = await Session.findById(sessionId);
  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }
  
  // Get all polls in the session
  const polls = await Poll.find({ session: sessionId });
  
  // Calculate leaderboard
  const participantStats = new Map();
  
  polls.forEach(poll => {
    poll.responses.forEach(response => {
      const userId = response.user.toString();
      if (!participantStats.has(userId)) {
        participantStats.set(userId, {
          userId,
          totalResponses: 0,
          correctAnswers: 0,
          totalResponseTime: 0,
          totalPoints: 0
        });
      }
      
      const stats = participantStats.get(userId);
      stats.totalResponses++;
      if (response.isCorrect) stats.correctAnswers++;
      stats.totalResponseTime += response.responseTime;
      
      // Calculate points (correct answer + speed bonus)
      const basePoints = response.isCorrect ? 10 : 0;
      const speedBonus = response.responseTime < 5000 ? 5 : 0;
      stats.totalPoints += basePoints + speedBonus;
    });
  });
  
  // Convert to array and populate user data
  const leaderboardData = Array.from(participantStats.values());
  
  // Get user details
  const userIds = leaderboardData.map(stat => stat.userId);
  const users = await User.find({ _id: { $in: userIds } }).select('name email avatar');
  
  const leaderboard = leaderboardData
    .map(stat => {
      const user = users.find(u => u._id.toString() === stat.userId);
      return {
        user: {
          _id: user?._id,
          name: user?.name,
          email: user?.email,
          avatar: user?.avatar
        },
        stats: {
          totalResponses: stat.totalResponses,
          correctAnswers: stat.correctAnswers,
          accuracy: stat.totalResponses > 0 ? (stat.correctAnswers / stat.totalResponses) * 100 : 0,
          averageResponseTime: stat.totalResponses > 0 ? stat.totalResponseTime / stat.totalResponses : 0,
          totalPoints: stat.totalPoints
        }
      };
    })
    .sort((a, b) => b.stats.totalPoints - a.stats.totalPoints)
    .slice(0, Number(limit))
    .map((entry, index) => ({
      rank: index + 1,
      ...entry
    }));
  
  res.json({
    success: true,
    leaderboard,
    sessionInfo: {
      title: session.title,
      totalParticipants: session.participants.length,
      totalPolls: polls.length
    }
  });
});

export const exportAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId } = req.params;
  const { format = 'json' } = req.query;
  
  const session = await Session.findById(sessionId);
  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }
  
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to export analytics for this session' });
  }
  
  const polls = await Poll.find({ session: sessionId })
    .populate('responses.user', 'name email')
    .populate('creator', 'name email');
  
  const exportData = {
    session: {
      title: session.title,
      code: session.code,
      createdAt: session.createdAt,
      startTime: session.startTime,
      endTime: session.endTime
    },
    polls: polls.map(poll => ({
      title: poll.title,
      question: poll.question,
      options: poll.options,
      correctAnswer: poll.correctAnswer,
      difficulty: poll.difficulty,
      responses: poll.responses,
      analytics: poll.analytics
    })),
    summary: {
      totalPolls: polls.length,
      totalResponses: polls.reduce((sum, poll) => sum + poll.responses.length, 0),
      averageAccuracy: polls.length > 0 
        ? polls.reduce((sum, poll) => sum + poll.analytics.accuracyRate, 0) / polls.length 
        : 0
    }
  };
  
  if (format === 'csv') {
    // Convert to CSV format
    const csv = convertToCSV(exportData);
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="session-${sessionId}-analytics.csv"`);
    res.send(csv);
  } else {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="session-${sessionId}-analytics.json"`);
    res.json(exportData);
  }
});

// Helper functions
async function getTopPerformers(sessionId: string) {
  const polls = await Poll.find({ session: sessionId });
  const participantStats = new Map();
  
  polls.forEach(poll => {
    poll.responses.forEach(response => {
      const userId = response.user.toString();
      if (!participantStats.has(userId)) {
        participantStats.set(userId, { correctAnswers: 0, totalResponses: 0 });
      }
      const stats = participantStats.get(userId);
      stats.totalResponses++;
      if (response.isCorrect) stats.correctAnswers++;
    });
  });
  
  const topPerformers = Array.from(participantStats.entries())
    .map(([userId, stats]) => ({
      userId,
      accuracy: stats.totalResponses > 0 ? (stats.correctAnswers / stats.totalResponses) * 100 : 0,
      totalResponses: stats.totalResponses
    }))
    .sort((a, b) => b.accuracy - a.accuracy)
    .slice(0, 5);
  
  const userIds = topPerformers.map(p => p.userId);
  const users = await User.find({ _id: { $in: userIds } }).select('name email');
  
  return topPerformers.map(performer => {
    const user = users.find(u => u._id.toString() === performer.userId);
    return {
      user: { name: user?.name, email: user?.email },
      accuracy: performer.accuracy,
      totalResponses: performer.totalResponses
    };
  });
}

function calculateDifficultyStats(responses: any[], difficulty: string) {
  const difficultyResponses = responses.filter(r => r.difficulty === difficulty);
  return {
    total: difficultyResponses.length,
    correct: difficultyResponses.filter(r => r.isCorrect).length,
    accuracy: difficultyResponses.length > 0 
      ? (difficultyResponses.filter(r => r.isCorrect).length / difficultyResponses.length) * 100 
      : 0
  };
}

async function getUserRanking(userId: string) {
  const users = await User.find({}).select('stats.totalPoints').sort({ 'stats.totalPoints': -1 });
  const userIndex = users.findIndex(u => u._id.toString() === userId);
  return {
    rank: userIndex + 1,
    totalUsers: users.length,
    percentile: users.length > 0 ? ((users.length - userIndex) / users.length) * 100 : 0
  };
}

function convertToCSV(data: any): string {
  // Simple CSV conversion - in production, use a proper CSV library
  let csv = 'Session Title,Poll Title,Question,Total Responses,Accuracy Rate,Average Response Time\n';
  
  data.polls.forEach((poll: any) => {
    csv += `"${data.session.title}","${poll.title}","${poll.question}",${poll.responses.length},${poll.analytics.accuracyRate},${poll.analytics.averageResponseTime}\n`;
  });
  
  return csv;
}
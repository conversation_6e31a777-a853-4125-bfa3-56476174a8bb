import mongoose from 'mongoose';
import performanceService from './performanceService.js';

class DatabaseOptimizationService {
  
  // Initialize database indexes for better query performance
  async createIndexes(): Promise<void> {
    try {
      console.log('🔧 Creating database indexes...');
      
      // User indexes
      await mongoose.connection.db.collection('users').createIndex({ email: 1 }, { unique: true });
      await mongoose.connection.db.collection('users').createIndex({ role: 1 });
      await mongoose.connection.db.collection('users').createIndex({ createdAt: -1 });
      
      // Session indexes
      await mongoose.connection.db.collection('sessions').createIndex({ code: 1 }, { unique: true });
      await mongoose.connection.db.collection('sessions').createIndex({ creator: 1 });
      await mongoose.connection.db.collection('sessions').createIndex({ isActive: 1 });
      await mongoose.connection.db.collection('sessions').createIndex({ createdAt: -1 });
      await mongoose.connection.db.collection('sessions').createIndex({ participants: 1 });
      await mongoose.connection.db.collection('sessions').createIndex({ 'settings.category': 1 });
      
      // Poll indexes
      await mongoose.connection.db.collection('polls').createIndex({ session: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ creator: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ isActive: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ createdAt: -1 });
      await mongoose.connection.db.collection('polls').createIndex({ category: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ type: 1 });
      
      // Compound indexes for common queries
      await mongoose.connection.db.collection('sessions').createIndex({ creator: 1, isActive: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ session: 1, isActive: 1 });
      await mongoose.connection.db.collection('polls').createIndex({ creator: 1, createdAt: -1 });
      
      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating database indexes:', error);
    }
  }

  // Optimized query wrapper with caching and performance tracking
  async optimizedFind<T>(
    model: any,
    query: any,
    options: {
      select?: string;
      populate?: any;
      sort?: any;
      limit?: number;
      skip?: number;
      lean?: boolean;
      cache?: boolean;
      cacheTTL?: number;
    } = {}
  ): Promise<T[]> {
    const {
      select,
      populate,
      sort,
      limit,
      skip,
      lean = true, // Use lean by default for better performance
      cache = false,
      cacheTTL = 300 // 5 minutes default cache
    } = options;

    return performanceService.trackDatabaseOperation(
      'find',
      model.collection.name,
      async () => {
        let queryBuilder = model.find(query);
        
        if (select) queryBuilder = queryBuilder.select(select);
        if (populate) queryBuilder = queryBuilder.populate(populate);
        if (sort) queryBuilder = queryBuilder.sort(sort);
        if (limit) queryBuilder = queryBuilder.limit(limit);
        if (skip) queryBuilder = queryBuilder.skip(skip);
        if (lean) queryBuilder = queryBuilder.lean();
        
        return await queryBuilder.exec();
      }
    );
  }

  // Optimized findOne with caching
  async optimizedFindOne<T>(
    model: any,
    query: any,
    options: {
      select?: string;
      populate?: any;
      lean?: boolean;
      cache?: boolean;
      cacheTTL?: number;
    } = {}
  ): Promise<T | null> {
    const {
      select,
      populate,
      lean = true,
      cache = false,
      cacheTTL = 300
    } = options;

    return performanceService.trackDatabaseOperation(
      'findOne',
      model.collection.name,
      async () => {
        let queryBuilder = model.findOne(query);
        
        if (select) queryBuilder = queryBuilder.select(select);
        if (populate) queryBuilder = queryBuilder.populate(populate);
        if (lean) queryBuilder = queryBuilder.lean();
        
        return await queryBuilder.exec();
      }
    );
  }

  // Optimized aggregation with performance tracking
  async optimizedAggregate<T>(
    model: any,
    pipeline: any[],
    options: {
      allowDiskUse?: boolean;
      cache?: boolean;
      cacheTTL?: number;
    } = {}
  ): Promise<T[]> {
    const { allowDiskUse = true } = options;

    return performanceService.trackDatabaseOperation(
      'aggregate',
      model.collection.name,
      async () => {
        return await model.aggregate(pipeline).option({ allowDiskUse }).exec();
      }
    );
  }

  // Batch operations for better performance
  async batchInsert<T>(model: any, documents: any[]): Promise<T[]> {
    return performanceService.trackDatabaseOperation(
      'insertMany',
      model.collection.name,
      async () => {
        return await model.insertMany(documents, { ordered: false });
      }
    );
  }

  async batchUpdate(
    model: any,
    filter: any,
    update: any,
    options: { upsert?: boolean } = {}
  ): Promise<any> {
    return performanceService.trackDatabaseOperation(
      'updateMany',
      model.collection.name,
      async () => {
        return await model.updateMany(filter, update, options);
      }
    );
  }

  // Connection pool optimization
  optimizeConnectionPool(): void {
    // Set optimal connection pool settings
    mongoose.set('maxPoolSize', 10); // Maximum number of connections
    mongoose.set('minPoolSize', 5);  // Minimum number of connections
    mongoose.set('maxIdleTimeMS', 30000); // Close connections after 30 seconds of inactivity
    mongoose.set('serverSelectionTimeoutMS', 5000); // How long to try selecting a server
    mongoose.set('socketTimeoutMS', 45000); // How long a send or receive on a socket can take
    
    console.log('🔧 Database connection pool optimized');
  }

  // Query analysis and suggestions
  async analyzeSlowQueries(): Promise<void> {
    try {
      // Enable profiling for slow operations (> 100ms)
      await mongoose.connection.db.admin().command({
        profile: 2,
        slowms: 100
      });
      
      console.log('📊 Database profiling enabled for slow queries');
    } catch (error) {
      console.error('❌ Error enabling database profiling:', error);
    }
  }

  // Database health check
  async healthCheck(): Promise<{
    connected: boolean;
    readyState: number;
    host: string;
    name: string;
    collections: number;
  }> {
    const connection = mongoose.connection;
    
    return {
      connected: connection.readyState === 1,
      readyState: connection.readyState,
      host: connection.host || 'unknown',
      name: connection.name || 'unknown',
      collections: Object.keys(connection.collections).length
    };
  }

  // Cleanup old data
  async cleanupOldData(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      // Clean up old inactive sessions
      const sessionsDeleted = await mongoose.connection.db.collection('sessions').deleteMany({
        isActive: false,
        updatedAt: { $lt: thirtyDaysAgo }
      });
      
      // Clean up old poll responses (keep for analytics)
      // This is more conservative - only clean up very old data
      const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
      const pollsDeleted = await mongoose.connection.db.collection('polls').deleteMany({
        isActive: false,
        createdAt: { $lt: sixMonthsAgo }
      });
      
      console.log(`🧹 Cleanup completed: ${sessionsDeleted.deletedCount} sessions, ${pollsDeleted.deletedCount} polls`);
    } catch (error) {
      console.error('❌ Error during data cleanup:', error);
    }
  }

  // Start periodic maintenance
  startPeriodicMaintenance(): void {
    // Run cleanup every 24 hours
    setInterval(() => {
      this.cleanupOldData();
    }, 24 * 60 * 60 * 1000);
    
    console.log('🔧 Periodic database maintenance started');
  }

  // Get database statistics
  async getStats(): Promise<{
    collections: Array<{
      name: string;
      count: number;
      size: number;
      avgObjSize: number;
    }>;
    indexes: Array<{
      collection: string;
      name: string;
      size: number;
    }>;
  }> {
    try {
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      
      const collectionStats = [];
      const indexStats = [];
      
      for (const collection of collections) {
        const stats = await db.collection(collection.name).stats();
        collectionStats.push({
          name: collection.name,
          count: stats.count || 0,
          size: stats.size || 0,
          avgObjSize: stats.avgObjSize || 0
        });
        
        const indexes = await db.collection(collection.name).indexes();
        for (const index of indexes) {
          indexStats.push({
            collection: collection.name,
            name: index.name,
            size: index.size || 0
          });
        }
      }
      
      return {
        collections: collectionStats,
        indexes: indexStats
      };
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      return { collections: [], indexes: [] };
    }
  }
}

// Create singleton instance
const dbOptimization = new DatabaseOptimizationService();

export default dbOptimization;

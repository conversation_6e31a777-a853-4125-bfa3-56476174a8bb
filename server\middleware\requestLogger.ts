import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AuthRequest } from './auth.js';
import logger from '../services/logger.js';

// Extend Request interface to include requestId
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
    }
  }
}

// Request ID middleware
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Generate or use existing request ID
  req.requestId = req.headers['x-request-id'] as string || uuidv4();
  
  // Set response header
  res.setHeader('X-Request-ID', req.requestId);
  
  next();
};

// Request timing middleware
export const requestTimingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.startTime = Date.now();
  next();
};

// Request logging middleware
export const requestLoggingMiddleware = (req: AuthRequest, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  req.startTime = startTime;

  // Log incoming request
  const requestData = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  };

  logger.http(`Incoming ${req.method} ${req.originalUrl}`, requestData);

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(this: Response, ...args: any[]) {
    const duration = Date.now() - startTime;
    
    // Log API request with response details
    logger.apiRequest(
      req.method,
      req.originalUrl,
      res.statusCode,
      duration,
      req.user?.id,
      req.ip
    );

    // Log slow requests
    if (duration > 1000) {
      logger.warn(`Slow request detected`, {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        duration: `${duration}ms`,
        statusCode: res.statusCode,
        userId: req.user?.id
      });
    }

    // Log error responses
    if (res.statusCode >= 400) {
      const logLevel = res.statusCode >= 500 ? 'error' : 'warn';
      logger[logLevel](`HTTP ${res.statusCode} response`, {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userId: req.user?.id,
        ip: req.ip
      });
    }

    originalEnd.apply(this, args);
  };

  next();
};

// Security logging middleware
export const securityLoggingMiddleware = (req: AuthRequest, res: Response, next: NextFunction) => {
  // Log authentication attempts
  if (req.path.includes('/auth/login') || req.path.includes('/auth/register')) {
    const email = req.body?.email;
    logger.auth(
      req.path.includes('/login') ? 'login_attempt' : 'register_attempt',
      req.user?.id,
      email,
      req.ip,
      true // Will be updated in error handler if failed
    );
  }

  // Log sensitive operations
  const sensitiveOperations = [
    '/auth/switch-role',
    '/auth/request-host-role',
    '/sessions',
    '/polls'
  ];

  if (sensitiveOperations.some(op => req.path.includes(op)) && req.method !== 'GET') {
    logger.security(`Sensitive operation: ${req.method} ${req.path}`, 'medium', {
      requestId: req.requestId,
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }

  // Log admin operations
  if (req.user?.role === 'admin') {
    logger.security(`Admin operation: ${req.method} ${req.path}`, 'low', {
      requestId: req.requestId,
      userId: req.user.id,
      ip: req.ip
    });
  }

  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
    /exec\(/i   // Command injection
  ];

  const fullUrl = req.originalUrl + JSON.stringify(req.body || {});
  if (suspiciousPatterns.some(pattern => pattern.test(fullUrl))) {
    logger.security('Suspicious request pattern detected', 'high', {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }

  next();
};

// Business logic logging middleware
export const businessLoggingMiddleware = (req: AuthRequest, res: Response, next: NextFunction) => {
  // Log session-related business events
  if (req.path.includes('/sessions')) {
    const sessionId = req.params.id || req.body?.sessionId;
    
    switch (req.method) {
      case 'POST':
        if (req.path.endsWith('/sessions')) {
          // Session creation
          logger.business('session_creation_attempt', {
            userId: req.user?.id,
            requestId: req.requestId
          });
        } else if (req.path.includes('/join')) {
          // Session join
          logger.session('join_attempt', sessionId, req.user?.id);
        } else if (req.path.includes('/start')) {
          // Session start
          logger.session('start_attempt', sessionId, req.user?.id);
        }
        break;
      case 'DELETE':
        // Session deletion
        logger.session('delete_attempt', sessionId, req.user?.id);
        break;
      case 'PUT':
        // Session update
        logger.session('update_attempt', sessionId, req.user?.id);
        break;
    }
  }

  // Log poll-related business events
  if (req.path.includes('/polls')) {
    const pollId = req.params.id || req.body?.pollId;
    
    switch (req.method) {
      case 'POST':
        if (req.path.endsWith('/polls')) {
          logger.business('poll_creation_attempt', {
            userId: req.user?.id,
            sessionId: req.body?.sessionId,
            requestId: req.requestId
          });
        } else if (req.path.includes('/vote')) {
          logger.business('vote_attempt', {
            userId: req.user?.id,
            pollId,
            requestId: req.requestId
          });
        }
        break;
    }
  }

  // Log authentication business events
  if (req.path.includes('/auth')) {
    switch (req.path) {
      case '/auth/switch-role':
        logger.business('role_switch_attempt', {
          userId: req.user?.id,
          targetRole: req.body?.role,
          requestId: req.requestId
        });
        break;
      case '/auth/request-host-role':
        logger.business('host_role_request', {
          userId: req.user?.id,
          requestId: req.requestId
        });
        break;
    }
  }

  next();
};

// Combined logging middleware
export const loggingMiddleware = [
  requestIdMiddleware,
  requestTimingMiddleware,
  requestLoggingMiddleware,
  securityLoggingMiddleware,
  businessLoggingMiddleware
];

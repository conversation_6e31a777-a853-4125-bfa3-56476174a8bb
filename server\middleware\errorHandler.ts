import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from './auth.js';
import logger from '../services/logger.js';
import config from '../config/env.js';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public status: string;
  public isOperational: boolean;
  public code?: string;
  public context?: any;

  constructor(message: string, statusCode: number = 500, code?: string, context?: any) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.code = code;
    this.context = context;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Async handler wrapper
export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Enhanced error handler
export const errorHandler = (err: any, req: AuthRequest, res: Response, next: NextFunction) => {
  let error = { ...err };
  error.message = err.message;

  // Create error context
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown'
  };

  // Log the error with context
  logger.error('Request Error', err, errorContext);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = new AppError(message, 404, 'CAST_ERROR');
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const message = `Duplicate ${field} entered`;
    error = new AppError(message, 400, 'DUPLICATE_FIELD');
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map((val: any) => val.message).join(', ');
    error = new AppError(message, 400, 'VALIDATION_ERROR');
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = new AppError(message, 401, 'INVALID_TOKEN');
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = new AppError(message, 401, 'TOKEN_EXPIRED');
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File too large';
    error = new AppError(message, 400, 'FILE_TOO_LARGE');
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    const message = 'Too many files';
    error = new AppError(message, 400, 'TOO_MANY_FILES');
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    error = new AppError(message, 400, 'UNEXPECTED_FILE');
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'Too many requests, please try again later';
    error = new AppError(message, 429, 'RATE_LIMIT_EXCEEDED');
  }

  // Database connection errors
  if (err.name === 'MongoNetworkError' || err.name === 'MongoTimeoutError') {
    const message = 'Database connection error';
    error = new AppError(message, 503, 'DATABASE_ERROR');

    // Log critical database errors
    logger.security('Database Connection Error', 'high', errorContext);
  }

  // Handle operational vs programming errors
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  // Security logging for suspicious activities
  if (statusCode === 401 || statusCode === 403) {
    logger.security('Unauthorized Access Attempt', 'medium', errorContext);
  }

  if (statusCode >= 500) {
    logger.security('Server Error', 'high', errorContext);
  }

  // Response format
  const errorResponse: any = {
    success: false,
    error: {
      message,
      ...(error.code && { code: error.code }),
      ...(config.NODE_ENV === 'development' && {
        stack: err.stack,
        context: errorContext
      })
    }
  };

  // Add request ID for tracking
  if (req.headers['x-request-id']) {
    errorResponse.requestId = req.headers['x-request-id'];
  }

  res.status(statusCode).json(errorResponse);
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const message = `Route ${req.originalUrl} not found`;
  const error = new AppError(message, 404, 'ROUTE_NOT_FOUND');

  logger.warn('Route Not Found', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  next(error);
};

// Validation error handler
export const validationErrorHandler = (errors: any[]) => {
  const message = errors.map(error => error.msg).join(', ');
  return new AppError(message, 400, 'VALIDATION_ERROR', { errors });
};

// Database error handler
export const handleDatabaseError = (operation: string, error: Error) => {
  logger.database(operation, 'unknown', 0, false, error);

  if (error.name === 'MongoNetworkError') {
    throw new AppError('Database connection failed', 503, 'DATABASE_CONNECTION_ERROR');
  }

  if (error.name === 'MongoTimeoutError') {
    throw new AppError('Database operation timed out', 503, 'DATABASE_TIMEOUT');
  }

  throw new AppError('Database operation failed', 500, 'DATABASE_ERROR');
};

// Utility functions for common errors
export const createNotFoundError = (resource: string, id?: string) => {
  const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
  return new AppError(message, 404, 'RESOURCE_NOT_FOUND');
};

export const createUnauthorizedError = (message: string = 'Unauthorized access') => {
  return new AppError(message, 401, 'UNAUTHORIZED');
};

export const createForbiddenError = (message: string = 'Access forbidden') => {
  return new AppError(message, 403, 'FORBIDDEN');
};

export const createValidationError = (message: string, details?: any) => {
  return new AppError(message, 400, 'VALIDATION_ERROR', details);
};

export const createConflictError = (message: string) => {
  return new AppError(message, 409, 'CONFLICT');
};

export const createTooManyRequestsError = (message: string = 'Too many requests') => {
  return new AppError(message, 429, 'TOO_MANY_REQUESTS');
};
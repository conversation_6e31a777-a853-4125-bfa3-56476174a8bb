import React, { useState } from 'react';
import { MessageCircle, X, Send, HelpCircle } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

const ChatbotAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: 'Hi! I\'m your PollFlow assistant. I can help you with questions about creating polls, managing sessions, and understanding results. How can I help you today?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');

  const faqResponses: { [key: string]: string } = {
    'how to join poll': 'To join a poll, you need to be in the same meeting session. Once the host starts a poll, it will appear automatically on your participant dashboard. Simply select your answer and click submit!',
    'how is scoring done': 'Scoring is based on correct answers and response time. You get points for each correct answer, with bonus points for faster responses. The leaderboard shows your current ranking.',
    'create poll': 'To create a poll, use the Voice Input feature to record discussion, and our AI will generate relevant questions automatically. You can also manually create questions using the Question Generator.',
    'export results': 'You can export poll results in CSV or PDF format using the Export Results panel. This includes participant statistics, question accuracy, and response times.',
    'meeting integration': 'Connect your Zoom or Google Meet to automatically generate polls from live discussions. Use the Meeting Integration panel to connect to your video platform.',
    'time limit': 'Poll time limits can be set when creating questions. Participants see a countdown timer, and polls automatically close when time expires.'
  };

  const quickQuestions = [
    'How to join a poll?',
    'How is scoring done?',
    'How to create a poll?',
    'Export poll results?'
  ];

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // Generate bot response
    setTimeout(() => {
      const botResponse = generateBotResponse(inputMessage);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: botResponse,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botMessage]);
    }, 1000);

    setInputMessage('');
  };

  const generateBotResponse = (userInput: string): string => {
    const lowercaseInput = userInput.toLowerCase();
    
    for (const [key, response] of Object.entries(faqResponses)) {
      if (lowercaseInput.includes(key)) {
        return response;
      }
    }

    // Default responses for common patterns
    if (lowercaseInput.includes('help') || lowercaseInput.includes('?')) {
      return 'I can help you with:\n• Joining and participating in polls\n• Creating and managing polls\n• Understanding scoring and results\n• Exporting data\n• Meeting integrations\n\nWhat specific topic would you like to know about?';
    }

    if (lowercaseInput.includes('thank') || lowercaseInput.includes('thanks')) {
      return 'You\'re welcome! Is there anything else I can help you with regarding PollFlow?';
    }

    return 'I\'m not sure about that specific question, but I can help you with poll creation, participation, scoring, results export, and meeting integrations. Could you please rephrase your question or choose from the suggested topics?';
  };

  const handleQuickQuestion = (question: string) => {
    setInputMessage(question);
    handleSendMessage();
  };

  return (
    <>
      {/* Chat Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg transition-all z-50 ${
          isOpen ? 'bg-red-600 hover:bg-red-700' : 'bg-purple-600 hover:bg-purple-700'
        }`}
      >
        {isOpen ? (
          <X className="h-6 w-6 text-white mx-auto" />
        ) : (
          <MessageCircle className="h-6 w-6 text-white mx-auto" />
        )}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-80 h-96 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 shadow-xl z-40 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-white/20">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                <HelpCircle className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-white font-medium">PollFlow Assistant</h3>
                <p className="text-gray-300 text-xs">Ask me anything!</p>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs p-3 rounded-lg text-sm ${
                    message.type === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-black/20 text-gray-200 border border-gray-600'
                  }`}
                >
                  <p className="whitespace-pre-line">{message.content}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Questions */}
          {messages.length === 1 && (
            <div className="p-3 border-t border-white/20">
              <p className="text-gray-300 text-xs mb-2">Quick questions:</p>
              <div className="space-y-1">
                {quickQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickQuestion(question)}
                    className="w-full text-left p-2 bg-white/10 hover:bg-white/20 rounded text-xs text-gray-300 transition-colors"
                  >
                    {question}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t border-white/20">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Type your question..."
                className="flex-1 p-2 bg-white/10 border border-gray-600 rounded-lg text-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim()}
                className="p-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
              >
                <Send className="h-4 w-4 text-white" />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatbotAssistant;
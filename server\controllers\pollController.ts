import { Response } from 'express';
import Poll from '../models/Poll.js';
import Session from '../models/Session.js';
import User from '../models/User.js';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { io } from '../index.js';

export const createPoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const {
    title,
    question,
    options,
    correctAnswer,
    type,
    category,
    difficulty,
    tags,
    sessionId,
    timeLimit,
    aiGenerated,
    aiConfidence
  } = req.body;

  // Verify session exists and user has permission
  const session = await Session.findById(sessionId);
  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to create polls in this session' });
  }

  // Create poll
  const poll = await Poll.create({
    title,
    question,
    options: options.map((opt: any) => ({ text: opt.text, votes: 0, voters: [] })),
    correctAnswer,
    type,
    category,
    difficulty: difficulty || 'Medium',
    tags: tags || [],
    creator: req.user?._id,
    session: sessionId,
    timeLimit: timeLimit || 30,
    aiGenerated: aiGenerated || false,
    aiConfidence
  });

  // Update session
  session.polls.push(poll._id);
  session.analytics.totalPolls += 1;
  await session.save();

  // Update user stats
  await User.findByIdAndUpdate(req.user?._id, {
    $inc: { 'stats.pollsCreated': 1 }
  });

  await poll.populate('creator', 'name email');

  res.status(201).json({
    success: true,
    message: 'Poll created successfully',
    poll
  });
});

export const getPolls = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId, category, difficulty, page = 1, limit = 10 } = req.query;
  
  const filter: any = {};
  if (sessionId) filter.session = sessionId;
  if (category) filter.category = category;
  if (difficulty) filter.difficulty = difficulty;

  // Only show published polls to participants, all polls to hosts/admins
  if (req.user?.role === 'participant') {
    filter.isPublished = true;
  } else if (req.user?.role === 'host') {
    filter.creator = req.user._id;
  }

  const polls = await Poll.find(filter)
    .populate('creator', 'name email')
    .populate('session', 'title code')
    .sort({ createdAt: -1 })
    .limit(Number(limit))
    .skip((Number(page) - 1) * Number(limit));

  const total = await Poll.countDocuments(filter);

  res.json({
    success: true,
    polls,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: Math.ceil(total / Number(limit))
    }
  });
});

export const getPollById = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id)
    .populate('creator', 'name email')
    .populate('session', 'title code')
    .populate('responses.user', 'name email');

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator._id.toString() !== req.user?._id.toString() && 
      req.user?.role !== 'admin' && 
      !poll.isPublished) {
    return res.status(403).json({ message: 'Access denied' });
  }

  res.json({
    success: true,
    poll
  });
});

export const updatePoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to update this poll' });
  }

  // Don't allow updates if poll is active
  if (poll.isActive) {
    return res.status(400).json({ message: 'Cannot update active poll' });
  }

  const updatedPoll = await Poll.findByIdAndUpdate(
    req.params.id,
    { ...req.body, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('creator', 'name email');

  res.json({
    success: true,
    message: 'Poll updated successfully',
    poll: updatedPoll
  });
});

export const deletePoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to delete this poll' });
  }

  // Don't allow deletion if poll is active
  if (poll.isActive) {
    return res.status(400).json({ message: 'Cannot delete active poll' });
  }

  await Poll.findByIdAndDelete(req.params.id);

  // Remove from session
  await Session.findByIdAndUpdate(poll.session, {
    $pull: { polls: poll._id },
    $inc: { 'analytics.totalPolls': -1 }
  });

  res.json({
    success: true,
    message: 'Poll deleted successfully'
  });
});

export const submitResponse = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { selectedOption, responseTime } = req.body;
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  if (!poll.isActive) {
    return res.status(400).json({ message: 'Poll is not active' });
  }

  // Check if user already responded
  const existingResponse = poll.responses.find(
    r => r.user.toString() === req.user?._id.toString()
  );

  if (existingResponse) {
    return res.status(400).json({ message: 'You have already responded to this poll' });
  }

  // Validate selected option
  if (selectedOption < 0 || selectedOption >= poll.options.length) {
    return res.status(400).json({ message: 'Invalid option selected' });
  }

  // Check if correct answer
  const isCorrect = poll.correctAnswer !== undefined && poll.correctAnswer === selectedOption;

  // Add response
  poll.responses.push({
    user: req.user?._id!,
    selectedOption,
    responseTime,
    isCorrect,
    submittedAt: new Date()
  });

  // Update option votes
  poll.options[selectedOption].votes += 1;
  poll.options[selectedOption].voters.push(req.user?._id!);
  poll.totalVotes += 1;

  // Update analytics
  const totalResponses = poll.responses.length;
  const totalResponseTime = poll.responses.reduce((sum, r) => sum + r.responseTime, 0);
  const correctResponses = poll.responses.filter(r => r.isCorrect).length;

  poll.analytics.averageResponseTime = totalResponseTime / totalResponses;
  poll.analytics.accuracyRate = (correctResponses / totalResponses) * 100;

  await poll.save();

  // Update user stats
  const pointsEarned = isCorrect ? 10 : 5; // Base points
  const speedBonus = responseTime < 5000 ? 5 : 0; // Bonus for quick response
  const totalPoints = pointsEarned + speedBonus;

  await User.findByIdAndUpdate(req.user?._id, {
    $inc: {
      'stats.pollsParticipated': 1,
      'stats.correctAnswers': isCorrect ? 1 : 0,
      'stats.totalPoints': totalPoints
    }
  });

  // Emit real-time update
  io.to(`session-${poll.session}`).emit('pollResponse', {
    pollId: poll._id,
    totalVotes: poll.totalVotes,
    options: poll.options.map(opt => ({
      text: opt.text,
      votes: opt.votes,
      percentage: poll.totalVotes > 0 ? (opt.votes / poll.totalVotes) * 100 : 0
    }))
  });

  res.json({
    success: true,
    message: 'Response submitted successfully',
    isCorrect,
    pointsEarned: totalPoints,
    results: poll.options.map(opt => ({
      text: opt.text,
      votes: opt.votes,
      percentage: poll.totalVotes > 0 ? (opt.votes / poll.totalVotes) * 100 : 0
    }))
  });
});

export const getActivePoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId } = req.params;
  
  const activePoll = await Poll.findOne({
    session: sessionId,
    isActive: true
  }).populate('creator', 'name email');

  if (!activePoll) {
    return res.json({
      success: true,
      poll: null,
      message: 'No active poll in this session'
    });
  }

  // Check if user already responded
  const userResponse = activePoll.responses.find(
    r => r.user.toString() === req.user?._id.toString()
  );

  res.json({
    success: true,
    poll: {
      ...activePoll.toObject(),
      hasResponded: !!userResponse,
      userResponse: userResponse || null
    }
  });
});

export const startPoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to start this poll' });
  }

  if (poll.isActive) {
    return res.status(400).json({ message: 'Poll is already active' });
  }

  // End any other active polls in the same session
  await Poll.updateMany(
    { session: poll.session, isActive: true },
    { isActive: false, endTime: new Date() }
  );

  // Start this poll
  poll.isActive = true;
  poll.isPublished = true;
  poll.startTime = new Date();
  
  if (poll.timeLimit) {
    poll.endTime = new Date(Date.now() + poll.timeLimit * 1000);
  }

  await poll.save();

  // Emit to all session participants
  io.to(`session-${poll.session}`).emit('pollStarted', {
    poll: {
      _id: poll._id,
      title: poll.title,
      question: poll.question,
      options: poll.options.map(opt => ({ text: opt.text })),
      type: poll.type,
      timeLimit: poll.timeLimit,
      startTime: poll.startTime
    }
  });

  // Auto-end poll after time limit
  if (poll.timeLimit) {
    setTimeout(async () => {
      await Poll.findByIdAndUpdate(poll._id, {
        isActive: false,
        endTime: new Date()
      });
      
      io.to(`session-${poll.session}`).emit('pollEnded', {
        pollId: poll._id
      });
    }, poll.timeLimit * 1000);
  }

  res.json({
    success: true,
    message: 'Poll started successfully',
    poll
  });
});

export const endPoll = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to end this poll' });
  }

  if (!poll.isActive) {
    return res.status(400).json({ message: 'Poll is not active' });
  }

  poll.isActive = false;
  poll.endTime = new Date();
  await poll.save();

  // Emit to all session participants
  io.to(`session-${poll.session}`).emit('pollEnded', {
    pollId: poll._id,
    results: poll.options.map(opt => ({
      text: opt.text,
      votes: opt.votes,
      percentage: poll.totalVotes > 0 ? (opt.votes / poll.totalVotes) * 100 : 0
    }))
  });

  res.json({
    success: true,
    message: 'Poll ended successfully',
    poll
  });
});

export const getPollResults = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id)
    .populate('responses.user', 'name email')
    .populate('creator', 'name email');

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  const results = {
    poll: {
      _id: poll._id,
      title: poll.title,
      question: poll.question,
      type: poll.type,
      totalVotes: poll.totalVotes,
      correctAnswer: poll.correctAnswer,
      analytics: poll.analytics
    },
    options: poll.options.map((opt, index) => ({
      text: opt.text,
      votes: opt.votes,
      percentage: poll.totalVotes > 0 ? (opt.votes / poll.totalVotes) * 100 : 0,
      isCorrect: poll.correctAnswer === index
    })),
    responses: poll.responses.map(response => ({
      user: response.user,
      selectedOption: response.selectedOption,
      responseTime: response.responseTime,
      isCorrect: response.isCorrect,
      submittedAt: response.submittedAt
    }))
  };

  res.json({
    success: true,
    results
  });
});

export const getPollAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const poll = await Poll.findById(req.params.id);

  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check permissions
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to view analytics' });
  }

  const analytics = {
    overview: {
      totalResponses: poll.responses.length,
      averageResponseTime: poll.analytics.averageResponseTime,
      accuracyRate: poll.analytics.accuracyRate,
      participationRate: poll.analytics.participationRate
    },
    optionBreakdown: poll.options.map((opt, index) => ({
      option: opt.text,
      votes: opt.votes,
      percentage: poll.totalVotes > 0 ? (opt.votes / poll.totalVotes) * 100 : 0,
      isCorrect: poll.correctAnswer === index
    })),
    responseTimeDistribution: {
      fast: poll.responses.filter(r => r.responseTime < 5000).length,
      medium: poll.responses.filter(r => r.responseTime >= 5000 && r.responseTime < 15000).length,
      slow: poll.responses.filter(r => r.responseTime >= 15000).length
    },
    correctnessAnalysis: poll.correctAnswer !== undefined ? {
      correct: poll.responses.filter(r => r.isCorrect).length,
      incorrect: poll.responses.filter(r => !r.isCorrect).length,
      accuracyBySpeed: {
        fast: poll.responses.filter(r => r.responseTime < 5000 && r.isCorrect).length,
        medium: poll.responses.filter(r => r.responseTime >= 5000 && r.responseTime < 15000 && r.isCorrect).length,
        slow: poll.responses.filter(r => r.responseTime >= 15000 && r.isCorrect).length
      }
    } : null
  };

  res.json({
    success: true,
    analytics
  });
});
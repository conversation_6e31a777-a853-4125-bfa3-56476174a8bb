import Redis from 'ioredis';
import config from '../config/env.js';

class CacheService {
  private redis: Redis | null = null;
  private isConnected = false;

  constructor() {
    this.connect();
  }

  private async connect() {
    try {
      if (config.REDIS_URL) {
        this.redis = new Redis(config.REDIS_URL, {
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true
        });

        this.redis.on('connect', () => {
          console.log('✅ Redis connected successfully');
          this.isConnected = true;
        });

        this.redis.on('error', (error) => {
          console.error('❌ Redis connection error:', error.message);
          this.isConnected = false;
        });

        this.redis.on('close', () => {
          console.log('🔌 Redis connection closed');
          this.isConnected = false;
        });

        await this.redis.connect();
      } else {
        console.log('⚠️  Redis URL not provided, caching disabled');
      }
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      this.isConnected = false;
    }
  }

  // Generic cache operations
  async get<T>(key: string): Promise<T | null> {
    if (!this.isConnected || !this.redis) return null;

    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds: number = 3600): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      await this.redis.setex(key, ttlSeconds, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      await this.redis.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async invalidatePattern(pattern: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
      return true;
    } catch (error) {
      console.error('Cache invalidate pattern error:', error);
      return false;
    }
  }

  // Specific cache methods for common use cases
  
  // Session caching
  async cacheSession(sessionId: string, sessionData: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`session:${sessionId}`, sessionData, ttl);
  }

  async getCachedSession(sessionId: string): Promise<any> {
    return this.get(`session:${sessionId}`);
  }

  async invalidateSession(sessionId: string): Promise<boolean> {
    return this.del(`session:${sessionId}`);
  }

  // User caching
  async cacheUser(userId: string, userData: any, ttl: number = 3600): Promise<boolean> {
    return this.set(`user:${userId}`, userData, ttl);
  }

  async getCachedUser(userId: string): Promise<any> {
    return this.get(`user:${userId}`);
  }

  async invalidateUser(userId: string): Promise<boolean> {
    return this.del(`user:${userId}`);
  }

  // Poll caching
  async cachePoll(pollId: string, pollData: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`poll:${pollId}`, pollData, ttl);
  }

  async getCachedPoll(pollId: string): Promise<any> {
    return this.get(`poll:${pollId}`);
  }

  async invalidatePoll(pollId: string): Promise<boolean> {
    return this.del(`poll:${pollId}`);
  }

  // Analytics caching
  async cacheAnalytics(key: string, data: any, ttl: number = 900): Promise<boolean> {
    return this.set(`analytics:${key}`, data, ttl);
  }

  async getCachedAnalytics(key: string): Promise<any> {
    return this.get(`analytics:${key}`);
  }

  // Rate limiting
  async incrementRateLimit(key: string, windowSeconds: number = 900): Promise<number> {
    if (!this.isConnected || !this.redis) return 0;

    try {
      const multi = this.redis.multi();
      multi.incr(key);
      multi.expire(key, windowSeconds);
      const results = await multi.exec();
      return results?.[0]?.[1] as number || 0;
    } catch (error) {
      console.error('Rate limit increment error:', error);
      return 0;
    }
  }

  async getRateLimit(key: string): Promise<number> {
    if (!this.isConnected || !this.redis) return 0;

    try {
      const count = await this.redis.get(key);
      return count ? parseInt(count) : 0;
    } catch (error) {
      console.error('Rate limit get error:', error);
      return 0;
    }
  }

  // Session participant tracking
  async addSessionParticipant(sessionId: string, userId: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      await this.redis.sadd(`session:${sessionId}:participants`, userId);
      return true;
    } catch (error) {
      console.error('Add session participant error:', error);
      return false;
    }
  }

  async removeSessionParticipant(sessionId: string, userId: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      await this.redis.srem(`session:${sessionId}:participants`, userId);
      return true;
    } catch (error) {
      console.error('Remove session participant error:', error);
      return false;
    }
  }

  async getSessionParticipantCount(sessionId: string): Promise<number> {
    if (!this.isConnected || !this.redis) return 0;

    try {
      return await this.redis.scard(`session:${sessionId}:participants`);
    } catch (error) {
      console.error('Get session participant count error:', error);
      return 0;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    if (!this.isConnected || !this.redis) return false;

    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Cache health check error:', error);
      return false;
    }
  }

  // Cleanup
  async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.disconnect();
      this.isConnected = false;
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

export default cacheService;

import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import VoiceInput from '../components/VoiceInput';
import LiveTranscription from '../components/LiveTranscription';
import QuestionGenerator from '../components/QuestionGenerator';
import AnalyticsDashboard from '../components/AnalyticsDashboard';
import QuestionReviewModal from '../components/QuestionReviewModal';
import ExportResults from '../components/ExportResults';
import MeetingIntegration from '../components/MeetingIntegration';
import ChatbotAssistant from '../components/ChatbotAssistant';
import { 
  Plus, 
  Users, 
  Zap, 
  BarChart3, 
  Mic, 
  Brain, 
  Trophy,
  Play,
  Pause,
  Square,
  Settings,
  FileText,
  Clock,
  Target,
  Activity,
  Sparkles
} from 'lucide-react';

const HostDashboard: React.FC = () => {
  const { logout } = useAuth();
  const [transcribedText, setTranscribedText] = useState('');
  const [generatedQuestions, setGeneratedQuestions] = useState<any[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [activePollSession, setActivePollSession] = useState<any>(null);

  const handleTranscriptionUpdate = (text: string) => {
    setTranscribedText(text);
  };

  const handleQuestionGenerated = (question: any) => {
    setGeneratedQuestions(prev => [...prev, question]);
  };

  const handleQuestionReview = (question: any) => {
    setSelectedQuestion(question);
    setShowReviewModal(true);
  };

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="space-y-8">
            {/* Welcome Header with Animation */}
            <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-2xl p-8 border border-purple-500/30 relative overflow-hidden">
              {/* Animated Background Elements */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute top-4 right-8 w-4 h-4 bg-purple-400 rounded-full animate-bounce"></div>
                <div className="absolute bottom-6 left-12 w-2 h-2 bg-pink-400 rounded-full animate-ping"></div>
                <div className="absolute top-12 left-20 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              </div>
              
              <div className="relative z-10">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-white mb-2">Welcome to AI Poll Studio</h1>
                    <p className="text-purple-200">Create intelligent, engaging polls with the power of AI</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 group">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-white">24</p>
                    <p className="text-sm text-gray-400">Active Participants</p>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
                </div>
              </div>

              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 group">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-white">12</p>
                    <p className="text-sm text-gray-400">Active Polls</p>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              </div>

              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 group">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-white">89%</p>
                    <p className="text-sm text-gray-400">Engagement Rate</p>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full animate-pulse" style={{ width: '89%' }}></div>
                </div>
              </div>

              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 group">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Brain className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-white">156</p>
                    <p className="text-sm text-gray-400">AI Questions Generated</p>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full animate-pulse" style={{ width: '95%' }}></div>
                </div>
              </div>
            </div>

            {/* Quick Actions Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
                <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-400" />
                  Quick Actions
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <button 
                    onClick={() => setActiveSection('create-session')}
                    className="p-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    <Plus className="h-6 w-6 mx-auto mb-2 group-hover:rotate-90 transition-transform duration-300" />
                    <span className="text-sm font-medium">New Session</span>
                  </button>
                  <button 
                    onClick={() => setActiveSection('audio-capture')}
                    className="p-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    <Mic className="h-6 w-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
                    <span className="text-sm font-medium">Voice Input</span>
                  </button>
                  <button 
                    onClick={() => setActiveSection('ai-questions')}
                    className="p-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    <Brain className="h-6 w-6 mx-auto mb-2 group-hover:pulse transition-transform duration-300" />
                    <span className="text-sm font-medium">AI Questions</span>
                  </button>
                  <button 
                    onClick={() => setActiveSection('reports')}
                    className="p-4 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg hover:from-orange-700 hover:to-red-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    <BarChart3 className="h-6 w-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
                    <span className="text-sm font-medium">Analytics</span>
                  </button>
                </div>
              </div>

              <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
                <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-400" />
                  Recent Activity
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 bg-purple-500/20 rounded-lg border border-purple-500/30">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300 text-sm">New poll session created</span>
                    <span className="text-xs text-gray-500 ml-auto">2 min ago</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-blue-500/20 rounded-lg border border-blue-500/30">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300 text-sm">AI generated 5 new questions</span>
                    <span className="text-xs text-gray-500 ml-auto">5 min ago</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-yellow-500/20 rounded-lg border border-yellow-500/30">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300 text-sm">15 participants joined</span>
                    <span className="text-xs text-gray-500 ml-auto">8 min ago</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'create-session':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-black/60 backdrop-blur-lg rounded-xl p-8 border border-purple-500/30">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <Plus className="h-6 w-6 mr-3 text-green-400" />
                Create New Poll Session
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Session Title</label>
                    <input
                      type="text"
                      placeholder="Enter session title..."
                      className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea
                      rows={4}
                      placeholder="Describe your poll session..."
                      className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 resize-none"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Duration (minutes)</label>
                      <input
                        type="number"
                        placeholder="30"
                        className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Max Participants</label>
                      <input
                        type="number"
                        placeholder="50"
                        className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Session Type</label>
                    <div className="grid grid-cols-1 gap-3">
                      <button className="p-4 bg-purple-600/20 border border-purple-500/30 rounded-lg text-left hover:bg-purple-600/30 transition-all duration-300">
                        <div className="flex items-center space-x-3">
                          <Brain className="h-5 w-5 text-purple-400" />
                          <div>
                            <p className="text-white font-medium">AI-Powered Session</p>
                            <p className="text-gray-400 text-sm">Auto-generate questions from audio</p>
                          </div>
                        </div>
                      </button>
                      <button className="p-4 bg-blue-600/20 border border-blue-500/30 rounded-lg text-left hover:bg-blue-600/30 transition-all duration-300">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-400" />
                          <div>
                            <p className="text-white font-medium">Manual Session</p>
                            <p className="text-gray-400 text-sm">Create questions manually</p>
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-4">
                    <h4 className="text-white font-medium mb-2">Session Preview</h4>
                    <div className="space-y-2 text-sm text-gray-300">
                      <p>• Real-time participant engagement</p>
                      <p>• AI-powered question generation</p>
                      <p>• Live analytics and leaderboards</p>
                      <p>• Automatic result compilation</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-4 mt-8">
                <button className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300">
                  Save as Draft
                </button>
                <button className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-lg transition-all duration-300 transform hover:scale-105">
                  Start Session
                </button>
              </div>
            </div>
          </div>
        );

      case 'audio-capture':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <VoiceInput 
              onTranscriptionUpdate={handleTranscriptionUpdate}
              isRecording={isRecording}
              setIsRecording={setIsRecording}
            />
            <LiveTranscription text={transcribedText} />
          </div>
        );

      case 'ai-questions':
        return (
          <QuestionGenerator 
            transcribedText={transcribedText}
            onQuestionGenerated={handleQuestionGenerated}
            onQuestionReview={handleQuestionReview}
          />
        );

      case 'manual-poll':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-black/60 backdrop-blur-lg rounded-xl p-8 border border-purple-500/30">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <FileText className="h-6 w-6 mr-3 text-blue-400" />
                Create Manual Poll
              </h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Question</label>
                  <textarea
                    rows={3}
                    placeholder="Enter your poll question..."
                    className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 resize-none"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Answer Options</label>
                  <div className="space-y-3">
                    {[1, 2, 3, 4].map((num) => (
                      <div key={num} className="flex items-center space-x-3">
                        <span className="text-gray-400 font-medium w-8">{String.fromCharCode(64 + num)}.</span>
                        <input
                          type="text"
                          placeholder={`Option ${num}`}
                          className="flex-1 p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                        <input
                          type="radio"
                          name="correct-answer"
                          className="w-4 h-4 text-purple-600 focus:ring-purple-500"
                        />
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-400 mt-2">Select the radio button next to the correct answer</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Time Limit (seconds)</label>
                    <input
                      type="number"
                      placeholder="30"
                      className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300">
                      <option>General Knowledge</option>
                      <option>Technology</option>
                      <option>Business</option>
                      <option>Education</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-4">
                  <button className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300">
                    Save as Draft
                  </button>
                  <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg transition-all duration-300 transform hover:scale-105">
                    Push Live
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'participants':
        return (
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-blue-400" />
              Active Participants (24)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 12 }, (_, i) => (
                <div key={i} className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 hover:bg-purple-500/30 transition-all duration-300">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold">{String.fromCharCode(65 + i)}</span>
                    </div>
                    <div>
                      <p className="text-white font-medium">Participant {i + 1}</p>
                      <p className="text-gray-400 text-sm">Score: {Math.floor(Math.random() * 100)}</p>
                    </div>
                    <div className="ml-auto w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'leaderboard':
        return (
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
              Live Leaderboard
            </h3>
            <AnalyticsDashboard />
          </div>
        );

      case 'reports':
        return <ExportResults />;

      case 'settings':
        return (
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Settings
            </h3>
            <p className="text-gray-300">Settings panel coming soon...</p>
          </div>
        );

      default:
        return (
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4">Dashboard</h3>
            <p className="text-gray-300">Welcome to your host dashboard!</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <Header />
      
      <div className="flex flex-1">
        <Sidebar 
          userRole="host" 
          activeSection={activeSection}
          onSectionChange={setActiveSection}
          onLogout={logout}
        />
        
        <main className="flex-1 p-8 overflow-y-auto bg-gradient-to-br from-black via-purple-900/20 to-black">
          {renderMainContent()}
        </main>
      </div>

      {/* Question Review Modal */}
      {showReviewModal && selectedQuestion && (
        <QuestionReviewModal
          question={selectedQuestion}
          onClose={() => setShowReviewModal(false)}
          onSave={(updatedQuestion) => {
            setShowReviewModal(false);
          }}
        />
      )}

      <ChatbotAssistant />
    </div>
  );
};

export default HostDashboard;
import mongoose, { Document, Schema } from 'mongoose';

export interface ISession extends Document {
  title: string;
  description?: string;
  code: string;
  creator: mongoose.Types.ObjectId;
  participants: mongoose.Types.ObjectId[];
  polls: mongoose.Types.ObjectId[];
  isActive: boolean;
  isPublic: boolean;
  maxParticipants: number;
  settings: {
    allowAnonymous: boolean;
    showResults: boolean;
    allowMultipleAttempts: boolean;
    autoLaunch: boolean;
    timerEnabled: boolean;
    defaultTimer: number;
  };
  analytics: {
    totalPolls: number;
    totalResponses: number;
    averageEngagement: number;
    peakParticipants: number;
  };
  startTime?: Date;
  endTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema<ISession>({
  title: {
    type: String,
    required: [true, 'Session title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    length: 6
  },
  creator: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  participants: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  polls: [{
    type: Schema.Types.ObjectId,
    ref: 'Poll'
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  maxParticipants: {
    type: Number,
    default: 100,
    min: 1,
    max: 1000
  },
  settings: {
    allowAnonymous: {
      type: Boolean,
      default: false
    },
    showResults: {
      type: Boolean,
      default: true
    },
    allowMultipleAttempts: {
      type: Boolean,
      default: false
    },
    autoLaunch: {
      type: Boolean,
      default: true
    },
    timerEnabled: {
      type: Boolean,
      default: true
    },
    defaultTimer: {
      type: Number,
      default: 30
    }
  },
  analytics: {
    totalPolls: {
      type: Number,
      default: 0
    },
    totalResponses: {
      type: Number,
      default: 0
    },
    averageEngagement: {
      type: Number,
      default: 0
    },
    peakParticipants: {
      type: Number,
      default: 0
    }
  },
  startTime: {
    type: Date,
    default: null
  },
  endTime: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Generate unique session code
sessionSchema.pre('save', function(next) {
  if (!this.code) {
    this.code = Math.random().toString(36).substring(2, 8).toUpperCase();
  }
  next();
});

// Indexes
sessionSchema.index({ code: 1 });
sessionSchema.index({ creator: 1, createdAt: -1 });
sessionSchema.index({ isActive: 1, isPublic: 1 });

export default mongoose.model<ISession>('Session', sessionSchema);
import { Response } from 'express';
import User from '../models/User.js';
import Poll from '../models/Poll.js';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';

export const getUsers = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { page = 1, limit = 10, role, search } = req.query;
  
  const filter: any = {};
  if (role) filter.role = role;
  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }

  const users = await User.find(filter)
    .select('-password')
    .sort({ createdAt: -1 })
    .limit(Number(limit))
    .skip((Number(page) - 1) * Number(limit));

  const total = await User.countDocuments(filter);

  res.json({
    success: true,
    users,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: Math.ceil(total / Number(limit))
    }
  });
});

export const getUserById = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await User.findById(req.params.id).select('-password');
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Users can only view their own profile unless they're admin
  if (req.user?._id.toString() !== req.params.id && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Access denied' });
  }

  res.json({
    success: true,
    user
  });
});

export const updateUserRole = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { role } = req.body;
  
  const user = await User.findById(req.params.id);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Record role change
  user.roleHistory.push({
    from: user.role,
    to: role,
    date: new Date(),
    reason: 'Admin role change',
    approvedBy: req.user?._id.toString()
  });

  user.role = role;
  await user.save();

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'User role updated successfully',
    user: userResponse
  });
});

export const getUserStats = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await User.findById(req.params.id).select('stats');
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Users can only view their own stats unless they're admin/host
  if (req.user?._id.toString() !== req.params.id && 
      !['admin', 'host'].includes(req.user?.role || '')) {
    return res.status(403).json({ message: 'Access denied' });
  }

  // Get additional statistics
  const pollsCreated = await Poll.countDocuments({ creator: req.params.id });
  const pollsParticipated = await Poll.countDocuments({
    'responses.user': req.params.id
  });

  const recentPolls = await Poll.find({
    'responses.user': req.params.id
  })
  .sort({ 'responses.submittedAt': -1 })
  .limit(5)
  .select('title question responses.$')
  .populate('creator', 'name');

  const stats = {
    ...user.stats,
    pollsCreated,
    pollsParticipated,
    accuracyRate: user.stats.pollsParticipated > 0 
      ? (user.stats.correctAnswers / user.stats.pollsParticipated) * 100 
      : 0,
    recentActivity: recentPolls
  };

  res.json({
    success: true,
    stats
  });
});

export const getUserAchievements = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await User.findById(req.params.id).select('achievements stats');
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Users can only view their own achievements unless they're admin/host
  if (req.user?._id.toString() !== req.params.id && 
      !['admin', 'host'].includes(req.user?.role || '')) {
    return res.status(403).json({ message: 'Access denied' });
  }

  // Define achievement criteria and check progress
  const achievementDefinitions = [
    {
      id: 'first_poll',
      title: 'First Steps',
      description: 'Complete your first poll',
      criteria: { pollsParticipated: 1 },
      points: 10,
      rarity: 'common'
    },
    {
      id: 'speed_demon',
      title: 'Speed Demon',
      description: 'Answer 5 questions in under 2 seconds each',
      criteria: { fastResponses: 5 },
      points: 50,
      rarity: 'rare'
    },
    {
      id: 'accuracy_master',
      title: 'Accuracy Master',
      description: 'Achieve 90% accuracy in 10 consecutive polls',
      criteria: { accuracyStreak: 10 },
      points: 100,
      rarity: 'epic'
    },
    {
      id: 'streak_master',
      title: 'Streak Master',
      description: 'Maintain a 7-day participation streak',
      criteria: { currentStreak: 7 },
      points: 75,
      rarity: 'rare'
    }
  ];

  const achievements = achievementDefinitions.map(achievement => ({
    ...achievement,
    unlocked: user.achievements.includes(achievement.id),
    progress: calculateAchievementProgress(achievement, user.stats)
  }));

  res.json({
    success: true,
    achievements,
    totalPoints: user.stats.totalPoints,
    unlockedCount: user.achievements.length
  });
});

export const updateUserPreferences = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await User.findById(req.params.id);
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Users can only update their own preferences
  if (req.user?._id.toString() !== req.params.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  user.preferences = { ...user.preferences, ...req.body };
  await user.save();

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    user: userResponse
  });
});

// Helper function to calculate achievement progress
function calculateAchievementProgress(achievement: any, stats: any): number {
  const criteria = achievement.criteria;
  
  if (criteria.pollsParticipated) {
    return Math.min(100, (stats.pollsParticipated / criteria.pollsParticipated) * 100);
  }
  
  if (criteria.currentStreak) {
    return Math.min(100, (stats.currentStreak / criteria.currentStreak) * 100);
  }
  
  // Add more criteria calculations as needed
  return 0;
}
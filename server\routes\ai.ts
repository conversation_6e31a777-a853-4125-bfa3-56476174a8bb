import express from 'express';
import { body } from 'express-validator';
import {
  generateQuestions,
  transcribeAudio,
  analyzeContent,
  getAIInsights
} from '../controllers/aiController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';
import multer from 'multer';

const router = express.Router();

// Configure multer for audio uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'));
    }
  }
});

// Generate questions from text
router.post('/generate-questions',
  authenticate,
  authorize('host', 'admin'),
  body('content').isLength({ min: 10 }).withMessage('Content must be at least 10 characters'),
  body('questionCount').optional().isInt({ min: 1, max: 10 }).withMessage('Question count must be between 1 and 10'),
  body('difficulty').optional().isIn(['Easy', 'Medium', 'Hard']).withMessage('Invalid difficulty level'),
  validate,
  generateQuestions
);

// Transcribe audio to text
router.post('/transcribe',
  authenticate,
  authorize('host', 'admin'),
  upload.single('audio'),
  transcribeAudio
);

// Analyze content for insights
router.post('/analyze',
  authenticate,
  authorize('host', 'admin'),
  body('content').isLength({ min: 10 }).withMessage('Content must be at least 10 characters'),
  validate,
  analyzeContent
);

// Get AI insights for a session
router.get('/insights/:sessionId',
  authenticate,
  authorize('host', 'admin'),
  getAIInsights
);

export default router;
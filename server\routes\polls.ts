import express from 'express';
import { body, param, query } from 'express-validator';
import {
  createPoll,
  getPolls,
  getPollById,
  updatePoll,
  deletePoll,
  submitResponse,
  getActivePoll,
  startPoll,
  endPoll,
  getPollResults,
  getPollAnalytics
} from '../controllers/pollController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Validation rules
const createPollValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('question')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Question must be between 1 and 1000 characters'),
  body('options')
    .isArray({ min: 2, max: 6 })
    .withMessage('Must provide between 2 and 6 options'),
  body('options.*.text')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Option text must be between 1 and 500 characters'),
  body('type')
    .isIn(['multiple-choice', 'true-false', 'short-answer', 'opinion-poll'])
    .withMessage('Invalid poll type'),
  body('category')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category is required'),
  body('timeLimit')
    .optional()
    .isInt({ min: 10, max: 300 })
    .withMessage('Time limit must be between 10 and 300 seconds')
];

const submitResponseValidation = [
  body('selectedOption')
    .isInt({ min: 0 })
    .withMessage('Selected option must be a valid number'),
  body('responseTime')
    .isInt({ min: 0 })
    .withMessage('Response time must be a positive number')
];

// Routes
router.post('/', authenticate, authorize('host', 'admin'), createPollValidation, validate, createPoll);
router.get('/', authenticate, getPolls);
router.get('/active/:sessionId', authenticate, getActivePoll);
router.get('/:id', authenticate, getPollById);
router.put('/:id', authenticate, authorize('host', 'admin'), updatePoll);
router.delete('/:id', authenticate, authorize('host', 'admin'), deletePoll);

router.post('/:id/submit', authenticate, submitResponseValidation, validate, submitResponse);
router.post('/:id/start', authenticate, authorize('host', 'admin'), startPoll);
router.post('/:id/end', authenticate, authorize('host', 'admin'), endPoll);

router.get('/:id/results', authenticate, getPollResults);
router.get('/:id/analytics', authenticate, authorize('host', 'admin'), getPollAnalytics);

export default router;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Environment configuration interface
interface EnvConfig {
  // Server
  NODE_ENV: string;
  PORT: number;
  
  // Database
  MONGODB_URI: string;
  
  // JWT
  JWT_SECRET: string;
  JWT_REFRESH_SECRET: string;
  JWT_EXPIRE: string;
  JWT_REFRESH_EXPIRE: string;
  
  // Client
  CLIENT_URL: string;
  
  // OpenAI (Optional)
  OPENAI_API_KEY?: string;
  
  // Redis (Optional)
  REDIS_URL?: string;
  
  // Email (Optional)
  EMAIL_HOST?: string;
  EMAIL_PORT?: number;
  EMAIL_USER?: string;
  EMAIL_PASS?: string;
  EMAIL_FROM?: string;
  
  // File Upload
  MAX_FILE_SIZE: number;
  UPLOAD_PATH: string;
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  
  // Security
  BCRYPT_ROUNDS: number;
  CORS_ORIGIN: string;
  SESSION_SECRET: string;
  
  // Logging
  LOG_LEVEL: string;
  LOG_FILE?: string;
  
  // WebRTC (Optional)
  TURN_SERVER_URL?: string;
  TURN_USERNAME?: string;
  TURN_CREDENTIAL?: string;
  
  // Whisper Service (Optional)
  WHISPER_SERVICE_URL?: string;
  WHISPER_MODEL?: string;
  
  // Analytics
  ANALYTICS_ENABLED: boolean;
  ANALYTICS_RETENTION_DAYS: number;
  
  // Backup
  BACKUP_ENABLED: boolean;
  BACKUP_INTERVAL?: string;
  BACKUP_RETENTION_DAYS: number;
}

// Default values
const defaults = {
  NODE_ENV: 'development',
  PORT: 5000,
  JWT_EXPIRE: '24h',
  JWT_REFRESH_EXPIRE: '7d',
  CLIENT_URL: 'http://localhost:5173',
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  UPLOAD_PATH: 'uploads',
  RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: 100,
  BCRYPT_ROUNDS: 12,
  CORS_ORIGIN: 'http://localhost:5173',
  LOG_LEVEL: 'info',
  ANALYTICS_ENABLED: true,
  ANALYTICS_RETENTION_DAYS: 90,
  BACKUP_ENABLED: false,
  BACKUP_RETENTION_DAYS: 30,
  WHISPER_MODEL: 'base'
};

// Required environment variables
const requiredVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'SESSION_SECRET'
];

// Validation functions
const validateRequired = (): void => {
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\n💡 Please check your .env file or environment configuration.');
    console.error('📋 See .env.example for reference.');
    process.exit(1);
  }
};

const validateValues = (): void => {
  const errors: string[] = [];
  
  // Validate NODE_ENV
  const validEnvs = ['development', 'production', 'test'];
  if (!validEnvs.includes(process.env.NODE_ENV || defaults.NODE_ENV)) {
    errors.push(`NODE_ENV must be one of: ${validEnvs.join(', ')}`);
  }
  
  // Validate PORT
  const port = parseInt(process.env.PORT || defaults.PORT.toString());
  if (isNaN(port) || port < 1 || port > 65535) {
    errors.push('PORT must be a valid port number (1-65535)');
  }
  
  // Validate JWT secrets in production
  if (process.env.NODE_ENV === 'production') {
    if (process.env.JWT_SECRET === 'your-super-secret-jwt-key-here-change-in-production') {
      errors.push('JWT_SECRET must be changed from default value in production');
    }
    if (process.env.JWT_REFRESH_SECRET === 'your-super-secret-refresh-key-here-change-in-production') {
      errors.push('JWT_REFRESH_SECRET must be changed from default value in production');
    }
    if (process.env.SESSION_SECRET === 'your-session-secret-here') {
      errors.push('SESSION_SECRET must be changed from default value in production');
    }
  }
  
  // Validate BCRYPT_ROUNDS
  const bcryptRounds = parseInt(process.env.BCRYPT_ROUNDS || defaults.BCRYPT_ROUNDS.toString());
  if (isNaN(bcryptRounds) || bcryptRounds < 10 || bcryptRounds > 15) {
    errors.push('BCRYPT_ROUNDS must be between 10 and 15');
  }
  
  // Validate EMAIL_PORT if provided
  if (process.env.EMAIL_PORT) {
    const emailPort = parseInt(process.env.EMAIL_PORT);
    if (isNaN(emailPort) || emailPort < 1 || emailPort > 65535) {
      errors.push('EMAIL_PORT must be a valid port number');
    }
  }
  
  if (errors.length > 0) {
    console.error('❌ Environment configuration errors:');
    errors.forEach(error => {
      console.error(`   - ${error}`);
    });
    process.exit(1);
  }
};

// Parse and validate configuration
const parseConfig = (): EnvConfig => {
  validateRequired();
  validateValues();
  
  return {
    NODE_ENV: process.env.NODE_ENV || defaults.NODE_ENV,
    PORT: parseInt(process.env.PORT || defaults.PORT.toString()),
    
    MONGODB_URI: process.env.MONGODB_URI!,
    
    JWT_SECRET: process.env.JWT_SECRET!,
    JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET!,
    JWT_EXPIRE: process.env.JWT_EXPIRE || defaults.JWT_EXPIRE,
    JWT_REFRESH_EXPIRE: process.env.JWT_REFRESH_EXPIRE || defaults.JWT_REFRESH_EXPIRE,
    
    CLIENT_URL: process.env.CLIENT_URL || defaults.CLIENT_URL,
    
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    
    REDIS_URL: process.env.REDIS_URL,
    
    EMAIL_HOST: process.env.EMAIL_HOST,
    EMAIL_PORT: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT) : undefined,
    EMAIL_USER: process.env.EMAIL_USER,
    EMAIL_PASS: process.env.EMAIL_PASS,
    EMAIL_FROM: process.env.EMAIL_FROM,
    
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || defaults.MAX_FILE_SIZE.toString()),
    UPLOAD_PATH: process.env.UPLOAD_PATH || defaults.UPLOAD_PATH,
    
    RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || defaults.RATE_LIMIT_WINDOW_MS.toString()),
    RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || defaults.RATE_LIMIT_MAX_REQUESTS.toString()),
    
    BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || defaults.BCRYPT_ROUNDS.toString()),
    CORS_ORIGIN: process.env.CORS_ORIGIN || defaults.CORS_ORIGIN,
    SESSION_SECRET: process.env.SESSION_SECRET!,
    
    LOG_LEVEL: process.env.LOG_LEVEL || defaults.LOG_LEVEL,
    LOG_FILE: process.env.LOG_FILE,
    
    TURN_SERVER_URL: process.env.TURN_SERVER_URL,
    TURN_USERNAME: process.env.TURN_USERNAME,
    TURN_CREDENTIAL: process.env.TURN_CREDENTIAL,
    
    WHISPER_SERVICE_URL: process.env.WHISPER_SERVICE_URL,
    WHISPER_MODEL: process.env.WHISPER_MODEL || defaults.WHISPER_MODEL,
    
    ANALYTICS_ENABLED: process.env.ANALYTICS_ENABLED === 'true' || defaults.ANALYTICS_ENABLED,
    ANALYTICS_RETENTION_DAYS: parseInt(process.env.ANALYTICS_RETENTION_DAYS || defaults.ANALYTICS_RETENTION_DAYS.toString()),
    
    BACKUP_ENABLED: process.env.BACKUP_ENABLED === 'true' || defaults.BACKUP_ENABLED,
    BACKUP_INTERVAL: process.env.BACKUP_INTERVAL,
    BACKUP_RETENTION_DAYS: parseInt(process.env.BACKUP_RETENTION_DAYS || defaults.BACKUP_RETENTION_DAYS.toString())
  };
};

// Export the configuration
export const config = parseConfig();

// Log configuration status
console.log('✅ Environment configuration loaded successfully');
console.log(`📊 Environment: ${config.NODE_ENV}`);
console.log(`🚀 Port: ${config.PORT}`);
console.log(`🔐 JWT Expiry: ${config.JWT_EXPIRE}`);
console.log(`📁 Upload Path: ${config.UPLOAD_PATH}`);
console.log(`🔄 Rate Limit: ${config.RATE_LIMIT_MAX_REQUESTS} requests per ${config.RATE_LIMIT_WINDOW_MS / 1000 / 60} minutes`);

export default config;

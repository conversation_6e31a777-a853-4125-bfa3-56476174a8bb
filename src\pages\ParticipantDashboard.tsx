import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import LivePoll from '../components/LivePoll';
import PollTimer from '../components/PollTimer';
import ChatbotAssistant from '../components/ChatbotAssistant';
import { Clock, Users, Trophy, BarChart3, MessageCircle } from 'lucide-react';

const ParticipantDashboard: React.FC = () => {
  const [currentPoll, setCurrentPoll] = useState<any>(null);
  const [pollResults, setPollResults] = useState<any>(null);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [userStats, setUserStats] = useState({
    pollsParticipated: 12,
    correctAnswers: 8,
    averageResponseTime: 2.3,
    rank: 5
  });

  // Simulate receiving a new poll
  useEffect(() => {
    const samplePoll = {
      id: '1',
      question: 'What is the most important factor for project success?',
      options: [
        'Clear communication',
        'Proper planning',
        'Team collaboration',
        'Regular feedback'
      ],
      timeLimit: 30,
      type: 'multiple-choice'
    };

    // Simulate poll starting after 2 seconds
    const timer = setTimeout(() => {
      if (activeSection === 'active-polls') {
        setCurrentPoll(samplePoll);
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [activeSection]);

  const handlePollSubmit = (selectedOption: number) => {
    // Simulate poll submission
    const results = {
      selectedOption,
      results: [25, 35, 20, 20], // percentages for each option
      correctAnswer: 1,
      totalVotes: 45
    };
    
    setPollResults(results);
    setCurrentPoll(null);
    
    // Update user stats
    setUserStats(prev => ({
      ...prev,
      pollsParticipated: prev.pollsParticipated + 1,
      correctAnswers: selectedOption === results.correctAnswer ? prev.correctAnswers + 1 : prev.correctAnswers
    }));
  };

  const handlePollTimeout = () => {
    setCurrentPoll(null);
    // Could show a "time's up" message
  };

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* User Stats */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
                Your Stats
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Polls Participated</span>
                  <span className="text-white font-semibold">{userStats.pollsParticipated}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Correct Answers</span>
                  <span className="text-green-400 font-semibold">{userStats.correctAnswers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Accuracy</span>
                  <span className="text-blue-400 font-semibold">
                    {Math.round((userStats.correctAnswers / userStats.pollsParticipated) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Current Rank</span>
                  <span className="text-yellow-400 font-semibold">#{userStats.rank}</span>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <MessageCircle className="h-5 w-5 mr-2 text-blue-400" />
                Recent Activity
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2 border-b border-gray-600">
                  <span className="text-gray-300 text-sm">Team Collaboration Poll</span>
                  <span className="text-green-400 text-xs">Correct</span>
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-600">
                  <span className="text-gray-300 text-sm">Project Management Poll</span>
                  <span className="text-red-400 text-xs">Incorrect</span>
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-600">
                  <span className="text-gray-300 text-sm">Communication Skills Poll</span>
                  <span className="text-green-400 text-xs">Correct</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button 
                  onClick={() => setActiveSection('active-polls')}
                  className="w-full p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Join Active Poll
                </button>
                <button 
                  onClick={() => setActiveSection('leaderboard')}
                  className="w-full p-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  View Leaderboard
                </button>
                <button 
                  onClick={() => setActiveSection('my-stats')}
                  className="w-full p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  My Statistics
                </button>
              </div>
            </div>
          </div>
        );
      case 'active-polls':
        return (
          <div className="max-w-4xl mx-auto">
            {currentPoll ? (
              <div className="space-y-6">
                <PollTimer 
                  timeLimit={currentPoll.timeLimit}
                  onTimeout={handlePollTimeout}
                />
                <LivePoll 
                  poll={currentPoll}
                  onSubmit={handlePollSubmit}
                />
              </div>
            ) : pollResults ? (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
                <h3 className="text-2xl font-semibold text-white mb-6">Poll Results</h3>
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <p className="text-green-400 text-lg font-semibold">
                      {pollResults.selectedOption === pollResults.correctAnswer ? 'Correct!' : 'Incorrect'}
                    </p>
                    <p className="text-gray-300">Total votes: {pollResults.totalVotes}</p>
                  </div>
                  
                  {pollResults.results.map((percentage: number, index: number) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className={`text-sm ${
                          index === pollResults.correctAnswer ? 'text-green-400' : 'text-gray-300'
                        }`}>
                          Option {index + 1} {index === pollResults.correctAnswer && '(Correct)'}
                        </span>
                        <span className="text-sm text-gray-300">{percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-1000 ${
                            index === pollResults.correctAnswer ? 'bg-green-500' : 'bg-purple-500'
                          }`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button 
                  onClick={() => setPollResults(null)}
                  className="w-full mt-6 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Ready for Next Poll
                </button>
              </div>
            ) : (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-12 text-center">
                <Clock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-2xl font-semibold text-white mb-2">Waiting for Poll</h3>
                <p className="text-gray-300">
                  The host will start a poll shortly. Stay tuned!
                </p>
                <div className="mt-6">
                  <div className="inline-flex items-center space-x-2 text-gray-400">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      case 'leaderboard':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
              Leaderboard
            </h3>
            <div className="space-y-3">
              {[
                { name: 'Alice Johnson', score: 95, rank: 1 },
                { name: 'Bob Smith', score: 88, rank: 2 },
                { name: 'Carol Davis', score: 82, rank: 3 },
                { name: 'David Wilson', score: 79, rank: 4 },
                { name: 'You', score: 75, rank: 5 }
              ].map((participant, index) => (
                <div key={index} className={`flex items-center justify-between p-3 rounded-lg ${
                  participant.name === 'You' ? 'bg-purple-600/20 border border-purple-500/30' : 'bg-black/20'
                }`}>
                  <div className="flex items-center space-x-3">
                    <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0 ? 'bg-yellow-500 text-black' :
                      index === 1 ? 'bg-gray-400 text-black' :
                      index === 2 ? 'bg-orange-600 text-white' :
                      'bg-purple-600 text-white'
                    }`}>
                      {participant.rank}
                    </span>
                    <span className={participant.name === 'You' ? 'text-purple-300 font-semibold' : 'text-gray-300'}>
                      {participant.name}
                    </span>
                  </div>
                  <span className="text-green-400 font-semibold">{participant.score}%</span>
                </div>
              ))}
            </div>
          </div>
        );
      case 'my-stats':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
                Performance Overview
              </h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Overall Accuracy</span>
                    <span className="text-blue-400 font-semibold">67%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '67%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Response Speed</span>
                    <span className="text-green-400 font-semibold">Fast</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Recent Achievements</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-2 bg-yellow-600/20 border border-yellow-500/30 rounded-lg">
                  <Trophy className="h-5 w-5 text-yellow-400" />
                  <span className="text-yellow-300 text-sm">Speed Demon - 5 quick answers</span>
                </div>
                <div className="flex items-center space-x-3 p-2 bg-green-600/20 border border-green-500/30 rounded-lg">
                  <Trophy className="h-5 w-5 text-green-400" />
                  <span className="text-green-300 text-sm">Accuracy Master - 80% correct</span>
                </div>
              </div>
            </div>
          </div>
        );
      case 'participants':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-blue-400" />
              Active Participants
            </h3>
            <div className="space-y-2">
              {['Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson', 'Emma Brown'].map((name, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <span className="text-gray-300">{name}</span>
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'poll-history':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-purple-400" />
              Poll History
            </h3>
            <p className="text-gray-300">Your poll history will appear here...</p>
          </div>
        );
      case 'settings':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Settings</h3>
            <p className="text-gray-300">Settings panel coming soon...</p>
          </div>
        );
      case 'help':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Help & Support</h3>
            <p className="text-gray-300">Help documentation coming soon...</p>
          </div>
        );
      default:
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Dashboard</h3>
            <p className="text-gray-300">Welcome to your participant dashboard!</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col">
      <Header />
      
      <div className="flex flex-1">
        <Sidebar 
          userRole="participant" 
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
        
        <main className="flex-1 p-8 overflow-y-auto">
          {renderMainContent()}
        </main>
      </div>

      <ChatbotAssistant />
    </div>
  );
};

export default ParticipantDashboard;
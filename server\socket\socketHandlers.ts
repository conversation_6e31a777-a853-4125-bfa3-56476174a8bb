import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import Session from '../models/Session.js';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

export const setupSocketHandlers = (io: Server) => {
  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as { userId: string };
      const user = await User.findById(decoded.userId).select('-password');
      
      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.user?.name} connected`);

    // Join session room
    socket.on('joinSession', async (sessionCode: string) => {
      try {
        const session = await Session.findOne({ code: sessionCode.toUpperCase() });
        
        if (!session) {
          socket.emit('error', { message: 'Session not found' });
          return;
        }

        if (!session.isActive) {
          socket.emit('error', { message: 'Session is not active' });
          return;
        }

        // Check if session is full
        if (session.participants.length >= session.maxParticipants) {
          socket.emit('error', { message: 'Session is full' });
          return;
        }

        // Add user to session if not already a participant
        if (!session.participants.includes(socket.userId!)) {
          session.participants.push(socket.userId!);
          await session.save();
        }

        // Join socket room
        socket.join(`session-${session._id}`);
        
        // Update peak participants
        if (session.participants.length > session.analytics.peakParticipants) {
          session.analytics.peakParticipants = session.participants.length;
          await session.save();
        }

        socket.emit('sessionJoined', {
          session: {
            _id: session._id,
            title: session.title,
            code: session.code,
            participantCount: session.participants.length
          }
        });

        // Notify other participants
        socket.to(`session-${session._id}`).emit('participantJoined', {
          user: {
            _id: socket.userId,
            name: socket.user?.name
          },
          participantCount: session.participants.length
        });

      } catch (error) {
        socket.emit('error', { message: 'Failed to join session' });
      }
    });

    // Leave session
    socket.on('leaveSession', async (sessionId: string) => {
      try {
        const session = await Session.findById(sessionId);
        
        if (session) {
          session.participants = session.participants.filter(
            p => p.toString() !== socket.userId
          );
          await session.save();

          socket.leave(`session-${sessionId}`);
          
          socket.to(`session-${sessionId}`).emit('participantLeft', {
            userId: socket.userId,
            participantCount: session.participants.length
          });
        }
      } catch (error) {
        console.error('Error leaving session:', error);
      }
    });

    // Real-time poll response
    socket.on('pollResponse', (data) => {
      const { pollId, sessionId, response } = data;
      
      // Broadcast response to session (without revealing the actual answer)
      socket.to(`session-${sessionId}`).emit('newResponse', {
        pollId,
        participantCount: 1 // This would be calculated properly
      });
    });

    // Host controls
    socket.on('startPoll', (data) => {
      const { pollId, sessionId } = data;
      
      // Only allow hosts to start polls
      if (socket.user?.role === 'host' || socket.user?.role === 'admin') {
        io.to(`session-${sessionId}`).emit('pollStarted', data);
      }
    });

    socket.on('endPoll', (data) => {
      const { pollId, sessionId } = data;
      
      // Only allow hosts to end polls
      if (socket.user?.role === 'host' || socket.user?.role === 'admin') {
        io.to(`session-${sessionId}`).emit('pollEnded', data);
      }
    });

    // Chat functionality
    socket.on('sendMessage', (data) => {
      const { sessionId, message } = data;
      
      const messageData = {
        _id: Date.now().toString(),
        user: {
          _id: socket.userId,
          name: socket.user?.name
        },
        message,
        timestamp: new Date()
      };
      
      io.to(`session-${sessionId}`).emit('newMessage', messageData);
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      console.log(`User ${socket.user?.name} disconnected`);
      
      // Remove user from all sessions they were in
      try {
        await Session.updateMany(
          { participants: socket.userId },
          { $pull: { participants: socket.userId } }
        );
      } catch (error) {
        console.error('Error removing user from sessions:', error);
      }
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('Socket error:', error);
      socket.emit('error', { message: 'An error occurred' });
    });
  });
};
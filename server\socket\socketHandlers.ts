import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import Session from '../models/Session.js';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
  currentSession?: string;
  isTyping?: boolean;
  lastActivity?: Date;
}

// Store active sessions and their participants
const activeSessions = new Map<string, Set<string>>();
const typingUsers = new Map<string, Map<string, NodeJS.Timeout>>();
const userPresence = new Map<string, { status: 'online' | 'away' | 'busy', lastSeen: Date }>();

export const setupSocketHandlers = (io: Server) => {
  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as { userId: string };
      const user = await User.findById(decoded.userId).select('-password');

      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      socket.lastActivity = new Date();

      // Set user as online
      userPresence.set(socket.userId, { status: 'online', lastSeen: new Date() });

      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.user?.name} connected`);

    // Join session room
    socket.on('joinSession', async (sessionCode: string) => {
      try {
        const session = await Session.findOne({ code: sessionCode.toUpperCase() });
        
        if (!session) {
          socket.emit('error', { message: 'Session not found' });
          return;
        }

        if (!session.isActive) {
          socket.emit('error', { message: 'Session is not active' });
          return;
        }

        // Check if session is full
        if (session.participants.length >= session.maxParticipants) {
          socket.emit('error', { message: 'Session is full' });
          return;
        }

        // Add user to session if not already a participant
        if (!session.participants.includes(socket.userId!)) {
          session.participants.push(socket.userId!);
          await session.save();
        }

        // Join socket room
        socket.join(`session-${session._id}`);
        socket.currentSession = session._id.toString();

        // Track active session participants
        if (!activeSessions.has(session._id.toString())) {
          activeSessions.set(session._id.toString(), new Set());
        }
        activeSessions.get(session._id.toString())?.add(socket.userId!);

        // Update peak participants
        if (session.participants.length > session.analytics.peakParticipants) {
          session.analytics.peakParticipants = session.participants.length;
          await session.save();
        }

        // Get online participants count
        const onlineParticipants = activeSessions.get(session._id.toString())?.size || 0;

        socket.emit('sessionJoined', {
          session: {
            _id: session._id,
            title: session.title,
            code: session.code,
            participantCount: session.participants.length,
            onlineParticipants
          }
        });

        // Notify other participants with live count update
        socket.to(`session-${session._id}`).emit('participantJoined', {
          user: {
            _id: socket.userId,
            name: socket.user?.name,
            avatar: socket.user?.avatar
          },
          participantCount: session.participants.length,
          onlineParticipants
        });

        // Send live participant count update
        io.to(`session-${session._id}`).emit('liveParticipantCount', {
          onlineParticipants,
          totalParticipants: session.participants.length
        });

      } catch (error) {
        socket.emit('error', { message: 'Failed to join session' });
      }
    });

    // Leave session
    socket.on('leaveSession', async (sessionId: string) => {
      try {
        const session = await Session.findById(sessionId);
        
        if (session) {
          session.participants = session.participants.filter(
            p => p.toString() !== socket.userId
          );
          await session.save();

          socket.leave(`session-${sessionId}`);
          
          socket.to(`session-${sessionId}`).emit('participantLeft', {
            userId: socket.userId,
            participantCount: session.participants.length
          });
        }
      } catch (error) {
        console.error('Error leaving session:', error);
      }
    });

    // Real-time poll response
    socket.on('pollResponse', (data) => {
      const { pollId, sessionId, response } = data;
      
      // Broadcast response to session (without revealing the actual answer)
      socket.to(`session-${sessionId}`).emit('newResponse', {
        pollId,
        participantCount: 1 // This would be calculated properly
      });
    });

    // Host controls
    socket.on('startPoll', (data) => {
      const { pollId, sessionId } = data;
      
      // Only allow hosts to start polls
      if (socket.user?.role === 'host' || socket.user?.role === 'admin') {
        io.to(`session-${sessionId}`).emit('pollStarted', data);
      }
    });

    socket.on('endPoll', (data) => {
      const { pollId, sessionId } = data;
      
      // Only allow hosts to end polls
      if (socket.user?.role === 'host' || socket.user?.role === 'admin') {
        io.to(`session-${sessionId}`).emit('pollEnded', data);
      }
    });

    // Chat functionality
    socket.on('sendMessage', (data) => {
      const { sessionId, message } = data;
      
      const messageData = {
        _id: Date.now().toString(),
        user: {
          _id: socket.userId,
          name: socket.user?.name
        },
        message,
        timestamp: new Date()
      };
      
      io.to(`session-${sessionId}`).emit('newMessage', messageData);
    });

    // Typing indicators
    socket.on('startTyping', (data) => {
      const { sessionId } = data;

      if (!socket.currentSession || socket.currentSession !== sessionId) return;

      socket.isTyping = true;
      socket.to(`session-${sessionId}`).emit('userStartedTyping', {
        userId: socket.userId,
        userName: socket.user?.name
      });

      // Clear existing typing timeout
      const sessionTyping = typingUsers.get(sessionId) || new Map();
      const existingTimeout = sessionTyping.get(socket.userId!);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Set timeout to auto-stop typing after 3 seconds
      const timeout = setTimeout(() => {
        socket.isTyping = false;
        socket.to(`session-${sessionId}`).emit('userStoppedTyping', {
          userId: socket.userId,
          userName: socket.user?.name
        });
        sessionTyping.delete(socket.userId!);
      }, 3000);

      sessionTyping.set(socket.userId!, timeout);
      typingUsers.set(sessionId, sessionTyping);
    });

    socket.on('stopTyping', (data) => {
      const { sessionId } = data;

      if (!socket.currentSession || socket.currentSession !== sessionId) return;

      socket.isTyping = false;
      socket.to(`session-${sessionId}`).emit('userStoppedTyping', {
        userId: socket.userId,
        userName: socket.user?.name
      });

      // Clear typing timeout
      const sessionTyping = typingUsers.get(sessionId);
      if (sessionTyping) {
        const timeout = sessionTyping.get(socket.userId!);
        if (timeout) {
          clearTimeout(timeout);
          sessionTyping.delete(socket.userId!);
        }
      }
    });

    // Poll progress tracking
    socket.on('pollProgress', async (data) => {
      const { pollId, sessionId, progress } = data;

      // Broadcast poll progress to session participants
      socket.to(`session-${sessionId}`).emit('pollProgressUpdate', {
        pollId,
        progress: {
          answered: progress.answered || 0,
          total: progress.total || 0,
          percentage: progress.percentage || 0
        }
      });
    });

    // User presence updates
    socket.on('updatePresence', (data) => {
      const { status } = data; // 'online', 'away', 'busy'

      if (['online', 'away', 'busy'].includes(status)) {
        userPresence.set(socket.userId!, { status, lastSeen: new Date() });

        // Broadcast presence update to all sessions user is in
        if (socket.currentSession) {
          socket.to(`session-${socket.currentSession}`).emit('userPresenceUpdate', {
            userId: socket.userId,
            status,
            lastSeen: new Date()
          });
        }
      }
    });

    // Activity tracking
    socket.on('userActivity', () => {
      socket.lastActivity = new Date();
      userPresence.set(socket.userId!, {
        status: userPresence.get(socket.userId!)?.status || 'online',
        lastSeen: new Date()
      });
    });

    // Screen sharing events
    socket.on('startScreenShare', (data) => {
      const { sessionId } = data;

      if (socket.user?.role === 'host' || socket.user?.role === 'admin') {
        socket.to(`session-${sessionId}`).emit('screenShareStarted', {
          userId: socket.userId,
          userName: socket.user?.name
        });
      }
    });

    socket.on('stopScreenShare', (data) => {
      const { sessionId } = data;

      socket.to(`session-${sessionId}`).emit('screenShareStopped', {
        userId: socket.userId,
        userName: socket.user?.name
      });
    });

    // Reaction events
    socket.on('sendReaction', (data) => {
      const { sessionId, reaction, targetType, targetId } = data;

      const reactionData = {
        userId: socket.userId,
        userName: socket.user?.name,
        reaction, // 👍, 👎, ❤️, 😂, 😮, 😢, 😡
        targetType, // 'poll', 'message', 'session'
        targetId,
        timestamp: new Date()
      };

      socket.to(`session-${sessionId}`).emit('newReaction', reactionData);
    });

    // Hand raising
    socket.on('raiseHand', (data) => {
      const { sessionId } = data;

      socket.to(`session-${sessionId}`).emit('handRaised', {
        userId: socket.userId,
        userName: socket.user?.name,
        timestamp: new Date()
      });
    });

    socket.on('lowerHand', (data) => {
      const { sessionId } = data;

      socket.to(`session-${sessionId}`).emit('handLowered', {
        userId: socket.userId,
        userName: socket.user?.name
      });
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      console.log(`User ${socket.user?.name} disconnected`);

      // Clean up user presence
      userPresence.set(socket.userId!, { status: 'away', lastSeen: new Date() });

      // Clean up active sessions tracking
      if (socket.currentSession) {
        const sessionParticipants = activeSessions.get(socket.currentSession);
        if (sessionParticipants) {
          sessionParticipants.delete(socket.userId!);

          // Notify remaining participants about updated online count
          const onlineParticipants = sessionParticipants.size;
          socket.to(`session-${socket.currentSession}`).emit('participantLeft', {
            userId: socket.userId,
            userName: socket.user?.name,
            onlineParticipants
          });

          socket.to(`session-${socket.currentSession}`).emit('liveParticipantCount', {
            onlineParticipants
          });
        }

        // Clean up typing indicators
        const sessionTyping = typingUsers.get(socket.currentSession);
        if (sessionTyping) {
          const timeout = sessionTyping.get(socket.userId!);
          if (timeout) {
            clearTimeout(timeout);
            sessionTyping.delete(socket.userId!);
          }

          // Notify that user stopped typing
          if (socket.isTyping) {
            socket.to(`session-${socket.currentSession}`).emit('userStoppedTyping', {
              userId: socket.userId,
              userName: socket.user?.name
            });
          }
        }
      }

      // Note: We don't remove users from session participants on disconnect
      // as they might reconnect. This should be handled by explicit leave actions
      // or session cleanup routines
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('Socket error:', error);
      socket.emit('error', { message: 'An error occurred' });
    });
  });

  // Periodic cleanup for inactive users (every 5 minutes)
  setInterval(() => {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    // Mark users as away if they haven't been active for 5 minutes
    for (const [userId, presence] of userPresence.entries()) {
      if (presence.lastSeen < fiveMinutesAgo && presence.status === 'online') {
        userPresence.set(userId, { status: 'away', lastSeen: presence.lastSeen });

        // Broadcast presence update to all connected sockets
        io.emit('userPresenceUpdate', {
          userId,
          status: 'away',
          lastSeen: presence.lastSeen
        });
      }
    }

    // Clean up old typing indicators
    for (const [sessionId, sessionTyping] of typingUsers.entries()) {
      for (const [userId, timeout] of sessionTyping.entries()) {
        // This cleanup is handled by the timeout itself, but we keep the structure clean
      }
    }
  }, 5 * 60 * 1000); // 5 minutes

  // Utility functions for external use
  const getSessionOnlineCount = (sessionId: string): number => {
    return activeSessions.get(sessionId)?.size || 0;
  };

  const getUserPresenceStatus = (userId: string) => {
    return userPresence.get(userId) || { status: 'away', lastSeen: new Date() };
  };

  const broadcastToSession = (sessionId: string, event: string, data: any) => {
    io.to(`session-${sessionId}`).emit(event, data);
  };

  // Export utility functions
  return {
    getSessionOnlineCount,
    getUserPresenceStatus,
    broadcastToSession
  };
};
import express from 'express';
import { body } from 'express-validator';
import {
  getUsers,
  getUserById,
  updateUserRole,
  getUserStats,
  getUserAchievements,
  updateUserPreferences
} from '../controllers/userController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Get all users (admin only)
router.get('/', authenticate, authorize('admin'), getUsers);

// Get user by ID
router.get('/:id', authenticate, getUserById);

// Update user role (admin only)
router.put('/:id/role', 
  authenticate, 
  authorize('admin'),
  body('role').isIn(['general', 'participant', 'host', 'admin']),
  validate,
  updateUserRole
);

// Get user statistics
router.get('/:id/stats', authenticate, getUserStats);

// Get user achievements
router.get('/:id/achievements', authenticate, getUserAchievements);

// Update user preferences
router.put('/:id/preferences', 
  authenticate,
  body('notifications').optional().isBoolean(),
  body('theme').optional().isIn(['light', 'dark']),
  body('language').optional().isLength({ min: 2, max: 5 }),
  validate,
  updateUserPreferences
);

export default router;
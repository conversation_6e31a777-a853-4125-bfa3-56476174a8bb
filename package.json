{"name": "live-meeting-poll-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\" \"npm run dev:whisper\"", "dev:client": "vite", "dev:server": "nodemon --exec tsx server/index.ts", "dev:whisper": "cd server/whisper && python main.py", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "start": "node dist/server/index.js", "lint": "eslint .", "preview": "vite preview", "setup:whisper": "cd server/whisper && python -m venv whisper-env && source whisper-env/bin/activate && pip install -r requirements.txt", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@types/ioredis": "^4.28.10", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "openai": "^4.24.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.0", "simple-peer": "^9.11.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.17.0", "ws": "^8.14.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/simple-peer": "^9.11.8", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.10", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^30.0.3", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.0.2", "postcss": "^8.4.35", "supertest": "^7.1.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0", "tsx": "^4.6.2", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}
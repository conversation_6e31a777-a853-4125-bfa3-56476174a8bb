{"name": "live-meeting-poll-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\" \"npm run dev:whisper\"", "dev:client": "vite", "dev:server": "nodemon --exec tsx server/index.ts", "dev:whisper": "cd server/whisper && python main.py", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "start": "node dist/server/index.js", "lint": "eslint .", "preview": "vite preview", "setup:whisper": "cd server/whisper && python -m venv whisper-env && source whisper-env/bin/activate && pip install -r requirements.txt"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.0", "socket.io-client": "^4.7.4", "simple-peer": "^9.11.1", "express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "uuid": "^9.0.1", "openai": "^4.24.1", "node-cron": "^3.0.3", "compression": "^1.7.4", "morgan": "^1.10.0", "ws": "^8.14.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "tsx": "^4.6.2", "@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/ws": "^8.5.10", "@types/simple-peer": "^9.11.8"}}
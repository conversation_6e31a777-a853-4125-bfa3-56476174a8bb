import React, { useState, useEffect } from 'react';
import { Clock, AlertTriangle } from 'lucide-react';

interface PollTimerProps {
  timeLimit: number; // in seconds
  onTimeout: () => void;
}

const PollTimer: React.FC<PollTimerProps> = ({ timeLimit, onTimeout }) => {
  const [timeLeft, setTimeLeft] = useState(timeLimit);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      onTimeout();
    }
  }, [timeLeft, onTimeout]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    const percentage = (timeLeft / timeLimit) * 100;
    if (percentage > 50) return 'text-green-400';
    if (percentage > 20) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getProgressColor = () => {
    const percentage = (timeLeft / timeLimit) * 100;
    if (percentage > 50) return 'bg-green-500';
    if (percentage > 20) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const progressPercentage = (timeLeft / timeLimit) * 100;

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-gray-300" />
          <span className="text-white font-medium">Poll Timer</span>
        </div>
        
        {timeLeft <= 10 && (
          <div className="flex items-center space-x-1 text-red-400">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">Hurry up!</span>
          </div>
        )}
      </div>

      <div className="text-center mb-3">
        <div className={`text-3xl font-bold font-mono ${getTimerColor()}`}>
          {formatTime(timeLeft)}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-1000 ${getProgressColor()}`}
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      <div className="text-center text-sm text-gray-400">
        Time remaining to answer
      </div>
    </div>
  );
};

export default PollTimer;
import React, { useState } from 'react';
import { 
  User, 
  Edit3, 
  Camera, 
  Mail, 
  Calendar, 
  MapPin, 
  Phone,
  Globe,
  Award,
  Trophy,
  Target,
  Clock,
  TrendingUp,
  Star,
  BookOpen,
  Zap,
  Save,
  X
} from 'lucide-react';

const StudentProfile: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: 'Alex',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    website: 'alexjohnson.dev',
    bio: 'Computer Science student passionate about web development and AI. Love participating in coding challenges and learning new technologies.',
    joinDate: new Date('2023-09-15'),
    birthDate: new Date('2001-03-22'),
    university: 'Stanford University',
    major: 'Computer Science',
    year: 'Junior'
  });

  const stats = {
    pollsParticipated: 247,
    accuracy: 87.5,
    averageResponseTime: 1.8,
    currentRank: 5,
    totalPoints: 12847,
    achievements: 23,
    studyHours: 156,
    streak: 12
  };

  const recentActivity = [
    { type: 'poll', title: 'React Fundamentals Quiz', score: 95, date: '2 hours ago' },
    { type: 'achievement', title: 'Speed Demon Unlocked', points: 50, date: '1 day ago' },
    { type: 'poll', title: 'JavaScript ES6 Challenge', score: 88, date: '2 days ago' },
    { type: 'rank', title: 'Moved to Rank #5', change: '+2', date: '3 days ago' }
  ];

  const handleSave = () => {
    setIsEditing(false);
    // Save profile data logic here
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data if needed
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <User className="h-6 w-6 mr-3 text-purple-400" />
          Student Profile
        </h3>
        <p className="text-purple-200">Manage your profile information and view your learning progress</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <div className="flex items-center justify-between mb-6">
            <h4 className="text-xl font-semibold text-white">Profile Information</h4>
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit3 className="h-4 w-4" />
                <span>Edit Profile</span>
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleCancel}
                  className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSave}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Save className="h-4 w-4" />
                  <span>Save</span>
                </button>
              </div>
            )}
          </div>

          {/* Profile Picture and Basic Info */}
          <div className="flex items-start space-x-6 mb-8">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                {profileData.firstName[0]}{profileData.lastName[0]}
              </div>
              {isEditing && (
                <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors">
                  <Camera className="h-4 w-4" />
                </button>
              )}
            </div>
            
            <div className="flex-1">
              {isEditing ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">First Name</label>
                    <input
                      type="text"
                      value={profileData.firstName}
                      onChange={(e) => setProfileData({...profileData, firstName: e.target.value})}
                      className="w-full p-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Last Name</label>
                    <input
                      type="text"
                      value={profileData.lastName}
                      onChange={(e) => setProfileData({...profileData, lastName: e.target.value})}
                      className="w-full p-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="text-2xl font-bold text-white">{profileData.firstName} {profileData.lastName}</h3>
                  <p className="text-purple-300">{profileData.major} • {profileData.year}</p>
                  <p className="text-gray-400">{profileData.university}</p>
                </div>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Mail className="h-4 w-4 inline mr-2" />
                Email Address
              </label>
              {isEditing ? (
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <p className="text-white">{profileData.email}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Phone className="h-4 w-4 inline mr-2" />
                Phone Number
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <p className="text-white">{profileData.phone}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="h-4 w-4 inline mr-2" />
                Location
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.location}
                  onChange={(e) => setProfileData({...profileData, location: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <p className="text-white">{profileData.location}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Globe className="h-4 w-4 inline mr-2" />
                Website
              </label>
              {isEditing ? (
                <input
                  type="url"
                  value={profileData.website}
                  onChange={(e) => setProfileData({...profileData, website: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <a href={`https://${profileData.website}`} className="text-blue-400 hover:text-blue-300 transition-colors">
                  {profileData.website}
                </a>
              )}
            </div>
          </div>

          {/* Bio */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
            {isEditing ? (
              <textarea
                value={profileData.bio}
                onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                rows={4}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              />
            ) : (
              <p className="text-gray-300">{profileData.bio}</p>
            )}
          </div>

          {/* Academic Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">University</label>
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.university}
                  onChange={(e) => setProfileData({...profileData, university: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <p className="text-white">{profileData.university}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Major</label>
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.major}
                  onChange={(e) => setProfileData({...profileData, major: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              ) : (
                <p className="text-white">{profileData.major}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Year</label>
              {isEditing ? (
                <select
                  value={profileData.year}
                  onChange={(e) => setProfileData({...profileData, year: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option>Freshman</option>
                  <option>Sophomore</option>
                  <option>Junior</option>
                  <option>Senior</option>
                  <option>Graduate</option>
                </select>
              ) : (
                <p className="text-white">{profileData.year}</p>
              )}
            </div>
          </div>
        </div>

        {/* Stats and Activity */}
        <div className="space-y-6">
          {/* Performance Stats */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
              Performance Stats
            </h4>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-blue-400" />
                  <span className="text-gray-300">Polls Participated</span>
                </div>
                <span className="text-white font-semibold">{stats.pollsParticipated}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Trophy className="h-4 w-4 text-yellow-400" />
                  <span className="text-gray-300">Accuracy Rate</span>
                </div>
                <span className="text-white font-semibold">{stats.accuracy}%</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-green-400" />
                  <span className="text-gray-300">Avg Response</span>
                </div>
                <span className="text-white font-semibold">{stats.averageResponseTime}s</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Award className="h-4 w-4 text-purple-400" />
                  <span className="text-gray-300">Current Rank</span>
                </div>
                <span className="text-white font-semibold">#{stats.currentRank}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-orange-400" />
                  <span className="text-gray-300">Total Points</span>
                </div>
                <span className="text-white font-semibold">{stats.totalPoints.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-4 w-4 text-cyan-400" />
                  <span className="text-gray-300">Study Hours</span>
                </div>
                <span className="text-white font-semibold">{stats.studyHours}h</span>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-4">Recent Activity</h4>
            
            <div className="space-y-3">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    activity.type === 'poll' ? 'bg-blue-600' :
                    activity.type === 'achievement' ? 'bg-yellow-600' :
                    activity.type === 'rank' ? 'bg-green-600' : 'bg-purple-600'
                  }`}>
                    {activity.type === 'poll' && <Target className="h-4 w-4 text-white" />}
                    {activity.type === 'achievement' && <Trophy className="h-4 w-4 text-white" />}
                    {activity.type === 'rank' && <TrendingUp className="h-4 w-4 text-white" />}
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">{activity.title}</p>
                    <p className="text-gray-400 text-xs">{activity.date}</p>
                  </div>
                  
                  <div className="text-right">
                    {activity.score && (
                      <p className="text-green-400 font-semibold text-sm">{activity.score}%</p>
                    )}
                    {activity.points && (
                      <p className="text-yellow-400 font-semibold text-sm">+{activity.points} pts</p>
                    )}
                    {activity.change && (
                      <p className="text-green-400 font-semibold text-sm">{activity.change}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Account Info */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-4">Account Information</h4>
            
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Member Since</span>
                <span className="text-white">{profileData.joinDate.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Birth Date</span>
                <span className="text-white">{profileData.birthDate.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Account Status</span>
                <span className="text-green-400">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentProfile;
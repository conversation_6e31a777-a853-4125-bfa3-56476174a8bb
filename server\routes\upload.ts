import express from 'express';
import { param, query } from 'express-validator';
import {
  uploadUserAvatar,
  uploadSessionMaterials,
  uploadPollAttachments,
  deleteUploadedFile,
  getFileInfo,
  getUserFiles
} from '../controllers/uploadController.js';
import {
  uploadAvatar,
  uploadSessionMaterial,
  uploadPollAttachment,
  handleUploadError
} from '../middleware/upload.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Upload user avatar
router.post('/avatar',
  authenticate,
  (req, res, next) => {
    uploadAvatar(req, res, (err) => {
      if (err) {
        return handleUploadError(err, req, res, next);
      }
      next();
    });
  },
  uploadUserAvatar
);

// Upload session materials
router.post('/session/:sessionId/materials',
  authenticate,
  param('sessionId').isMongoId().withMessage('Invalid session ID'),
  validate,
  (req, res, next) => {
    uploadSessionMaterial(req, res, (err) => {
      if (err) {
        return handleUploadError(err, req, res, next);
      }
      next();
    });
  },
  uploadSessionMaterials
);

// Upload poll attachments
router.post('/poll/:pollId/attachments',
  authenticate,
  param('pollId').isMongoId().withMessage('Invalid poll ID'),
  validate,
  (req, res, next) => {
    uploadPollAttachment(req, res, (err) => {
      if (err) {
        return handleUploadError(err, req, res, next);
      }
      next();
    });
  },
  uploadPollAttachments
);

// Delete uploaded file
router.delete('/file/:filename',
  authenticate,
  param('filename').notEmpty().withMessage('Filename is required'),
  query('type')
    .isIn(['avatar', 'session', 'poll'])
    .withMessage('Type must be one of: avatar, session, poll'),
  validate,
  deleteUploadedFile
);

// Get file info
router.get('/file/:filename',
  authenticate,
  param('filename').notEmpty().withMessage('Filename is required'),
  query('type')
    .isIn(['avatar', 'session', 'poll'])
    .withMessage('Type must be one of: avatar, session, poll'),
  validate,
  getFileInfo
);

// Get user's uploaded files
router.get('/my-files',
  authenticate,
  query('type')
    .optional()
    .isIn(['avatar', 'session', 'poll', 'all'])
    .withMessage('Type must be one of: avatar, session, poll, all'),
  validate,
  getUserFiles
);

export default router;

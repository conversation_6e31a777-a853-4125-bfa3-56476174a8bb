import React from 'react';
import { BarChart3, Users, Zap, Trophy } from 'lucide-react';

const AnalyticsDashboard: React.FC = () => {
  const topPerformers = [
    { name: '<PERSON>', score: 95, responses: 12 },
    { name: '<PERSON>', score: 88, responses: 11 },
    { name: '<PERSON>', score: 82, responses: 10 },
    { name: '<PERSON>', score: 79, responses: 9 },
    { name: '<PERSON>', score: 75, responses: 8 }
  ];

  const fastestResponders = [
    { name: '<PERSON>', time: 1.2 },
    { name: '<PERSON>', time: 1.8 },
    { name: '<PERSON>', time: 2.1 },
    { name: '<PERSON>', time: 2.5 },
    { name: '<PERSON>', time: 3.0 }
  ];

  const questionAccuracy = [
    { question: 'Q1: Team Collaboration', accuracy: 85 },
    { question: 'Q2: Project Management', accuracy: 72 },
    { question: 'Q3: Communication', accuracy: 91 },
    { question: 'Q4: Problem Solving', accuracy: 68 }
  ];

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
      <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
        <BarChart3 className="h-5 w-5 mr-2" />
        Analytics Dashboard
      </h3>

      <div className="space-y-6">
        {/* Top Performers */}
        <div>
          <h4 className="text-lg font-medium text-white mb-3 flex items-center">
            <Trophy className="h-4 w-4 mr-2 text-yellow-400" />
            Top Performers
          </h4>
          <div className="space-y-2">
            {topPerformers.map((performer, index) => (
              <div key={index} className="flex items-center justify-between bg-black/20 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-500 text-black' :
                    index === 1 ? 'bg-gray-400 text-black' :
                    index === 2 ? 'bg-orange-600 text-white' :
                    'bg-purple-600 text-white'
                  }`}>
                    {index + 1}
                  </span>
                  <span className="text-gray-300">{performer.name}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-green-400 font-semibold">{performer.score}%</span>
                  <span className="text-gray-400 text-sm">{performer.responses} answers</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Fastest Responders */}
        <div>
          <h4 className="text-lg font-medium text-white mb-3 flex items-center">
            <Zap className="h-4 w-4 mr-2 text-blue-400" />
            Fastest Responders
          </h4>
          <div className="space-y-2">
            {fastestResponders.map((responder, index) => (
              <div key={index} className="flex items-center justify-between bg-black/20 rounded-lg p-3">
                <span className="text-gray-300">{responder.name}</span>
                <span className="text-blue-400 font-semibold">{responder.time}s</span>
              </div>
            ))}
          </div>
        </div>

        {/* Per-Question Accuracy */}
        <div>
          <h4 className="text-lg font-medium text-white mb-3 flex items-center">
            <Users className="h-4 w-4 mr-2 text-purple-400" />
            Per-Question Accuracy
          </h4>
          <div className="space-y-3">
            {questionAccuracy.map((question, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">{question.question}</span>
                  <span className="text-purple-400 font-semibold">{question.accuracy}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${question.accuracy}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
import React, { useState } from 'react';
import { 
  Trophy, 
  Star, 
  Award, 
  Target, 
  Flame, 
  Zap, 
  Crown, 
  Shield,
  Medal,
  Gem,
  Clock,
  TrendingUp,
  Users,
  BookOpen
} from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'accuracy' | 'speed' | 'participation' | 'streak' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  unlockedDate?: Date;
  points: number;
}

const StudentAchievements: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const achievements: Achievement[] = [
    {
      id: '1',
      title: 'First Steps',
      description: 'Complete your first poll',
      icon: <Target className="h-6 w-6" />,
      category: 'participation',
      rarity: 'common',
      progress: 1,
      maxProgress: 1,
      unlocked: true,
      unlockedDate: new Date('2024-01-15'),
      points: 10
    },
    {
      id: '2',
      title: 'Speed Demon',
      description: 'Answer 5 questions in under 2 seconds each',
      icon: <Zap className="h-6 w-6" />,
      category: 'speed',
      rarity: 'rare',
      progress: 5,
      maxProgress: 5,
      unlocked: true,
      unlockedDate: new Date('2024-01-18'),
      points: 50
    },
    {
      id: '3',
      title: 'Accuracy Master',
      description: 'Achieve 90% accuracy in 10 consecutive polls',
      icon: <Trophy className="h-6 w-6" />,
      category: 'accuracy',
      rarity: 'epic',
      progress: 7,
      maxProgress: 10,
      unlocked: false,
      points: 100
    },
    {
      id: '4',
      title: 'Hot Streak',
      description: 'Maintain a 7-day participation streak',
      icon: <Flame className="h-6 w-6" />,
      category: 'streak',
      rarity: 'rare',
      progress: 7,
      maxProgress: 7,
      unlocked: true,
      unlockedDate: new Date('2024-01-20'),
      points: 75
    },
    {
      id: '5',
      title: 'Knowledge Seeker',
      description: 'Participate in 50 different polls',
      icon: <BookOpen className="h-6 w-6" />,
      category: 'participation',
      rarity: 'epic',
      progress: 24,
      maxProgress: 50,
      unlocked: false,
      points: 150
    },
    {
      id: '6',
      title: 'Perfect Score',
      description: 'Get 100% accuracy in a poll with 10+ questions',
      icon: <Star className="h-6 w-6" />,
      category: 'accuracy',
      rarity: 'legendary',
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      points: 200
    },
    {
      id: '7',
      title: 'Social Butterfly',
      description: 'Participate in polls with 100+ other students',
      icon: <Users className="h-6 w-6" />,
      category: 'participation',
      rarity: 'rare',
      progress: 67,
      maxProgress: 100,
      unlocked: false,
      points: 80
    },
    {
      id: '8',
      title: 'Lightning Fast',
      description: 'Answer a question in under 0.5 seconds',
      icon: <Zap className="h-6 w-6" />,
      category: 'speed',
      rarity: 'legendary',
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      points: 250
    }
  ];

  const categories = [
    { id: 'all', name: 'All Achievements', icon: <Award className="h-4 w-4" /> },
    { id: 'accuracy', name: 'Accuracy', icon: <Target className="h-4 w-4" /> },
    { id: 'speed', name: 'Speed', icon: <Zap className="h-4 w-4" /> },
    { id: 'participation', name: 'Participation', icon: <Users className="h-4 w-4" /> },
    { id: 'streak', name: 'Streaks', icon: <Flame className="h-4 w-4" /> },
    { id: 'special', name: 'Special', icon: <Crown className="h-4 w-4" /> }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-600 to-gray-700 border-gray-500';
      case 'rare': return 'from-blue-600 to-blue-700 border-blue-500';
      case 'epic': return 'from-purple-600 to-purple-700 border-purple-500';
      case 'legendary': return 'from-yellow-600 to-orange-600 border-yellow-500';
      default: return 'from-gray-600 to-gray-700 border-gray-500';
    }
  };

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'common': return <Shield className="h-4 w-4" />;
      case 'rare': return <Medal className="h-4 w-4" />;
      case 'epic': return <Crown className="h-4 w-4" />;
      case 'legendary': return <Gem className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(a => a.category === selectedCategory);

  const unlockedCount = achievements.filter(a => a.unlocked).length;
  const totalPoints = achievements.filter(a => a.unlocked).reduce((sum, a) => sum + a.points, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 backdrop-blur-lg rounded-xl p-6 border border-yellow-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <Trophy className="h-6 w-6 mr-3 text-yellow-400" />
          Achievements & Rewards
        </h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-yellow-200 text-sm">Unlocked</p>
            <p className="text-white text-2xl font-bold">{unlockedCount}/{achievements.length}</p>
          </div>
          <div className="text-center">
            <p className="text-yellow-200 text-sm">Total Points</p>
            <p className="text-white text-2xl font-bold">{totalPoints}</p>
          </div>
          <div className="text-center">
            <p className="text-yellow-200 text-sm">Completion</p>
            <p className="text-white text-2xl font-bold">{Math.round((unlockedCount / achievements.length) * 100)}%</p>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h4 className="text-xl font-semibold text-white mb-4">Categories</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`p-3 rounded-lg border transition-all ${
                selectedCategory === category.id
                  ? 'bg-purple-600 border-purple-500 text-white'
                  : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
              }`}
            >
              <div className="flex items-center space-x-2">
                {category.icon}
                <span className="text-sm font-medium">{category.name}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Achievements Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAchievements.map((achievement) => (
          <div
            key={achievement.id}
            className={`bg-gradient-to-br ${getRarityColor(achievement.rarity)} backdrop-blur-lg rounded-xl p-6 border transition-all hover:scale-105 ${
              achievement.unlocked ? 'opacity-100' : 'opacity-60'
            }`}
          >
            {/* Achievement Header */}
            <div className="flex items-start justify-between mb-4">
              <div className={`w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center ${
                achievement.unlocked ? 'text-white' : 'text-gray-400'
              }`}>
                {achievement.icon}
              </div>
              <div className="flex items-center space-x-1">
                {getRarityIcon(achievement.rarity)}
                <span className="text-xs text-white/80 capitalize">{achievement.rarity}</span>
              </div>
            </div>

            {/* Achievement Info */}
            <div className="mb-4">
              <h5 className="text-white font-semibold text-lg mb-1">{achievement.title}</h5>
              <p className="text-white/80 text-sm">{achievement.description}</p>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-white/80 mb-2">
                <span>Progress</span>
                <span>{achievement.progress}/{achievement.maxProgress}</span>
              </div>
              <div className="w-full bg-black/30 rounded-full h-2">
                <div
                  className="bg-white h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Achievement Footer */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-400" />
                <span className="text-white text-sm font-medium">{achievement.points} pts</span>
              </div>
              {achievement.unlocked && achievement.unlockedDate && (
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-white/60" />
                  <span className="text-xs text-white/60">
                    {achievement.unlockedDate.toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>

            {/* Unlocked Badge */}
            {achievement.unlocked && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <Trophy className="h-3 w-3 text-white" />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Recent Achievements */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
          Recent Achievements
        </h4>
        <div className="space-y-3">
          {achievements
            .filter(a => a.unlocked && a.unlockedDate)
            .sort((a, b) => (b.unlockedDate?.getTime() || 0) - (a.unlockedDate?.getTime() || 0))
            .slice(0, 3)
            .map((achievement) => (
              <div key={achievement.id} className="flex items-center space-x-4 p-3 bg-green-600/20 border border-green-500/30 rounded-lg">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  {achievement.icon}
                </div>
                <div className="flex-1">
                  <h5 className="text-white font-medium">{achievement.title}</h5>
                  <p className="text-green-300 text-sm">{achievement.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-green-400 font-semibold">+{achievement.points} pts</p>
                  <p className="text-green-300 text-xs">
                    {achievement.unlockedDate?.toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default StudentAchievements;
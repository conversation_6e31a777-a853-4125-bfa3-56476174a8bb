import React, { useState } from 'react';
import { 
  BarChart3, 
  Download, 
  Calendar, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  FileText,
  PieChart,
  Activity,
  Zap,
  Filter,
  RefreshCw
} from 'lucide-react';

const ReportsAnalytics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7');
  const [selectedFormat, setSelectedFormat] = useState('PDF');
  const [isGenerating, setIsGenerating] = useState(false);

  const reportTemplates = [
    {
      id: 'overview',
      title: 'Overview Report',
      description: 'Comprehensive summary of all polling activities',
      icon: <BarChart3 className="h-5 w-5" />,
      color: 'from-blue-600 to-cyan-600'
    },
    {
      id: 'performance',
      title: 'Performance Analysis',
      description: 'Detailed participant performance metrics',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'from-green-600 to-emerald-600'
    },
    {
      id: 'engagement',
      title: 'Engagement Report',
      description: 'Participation rates and engagement patterns',
      icon: <Users className="h-5 w-5" />,
      color: 'from-purple-600 to-pink-600'
    },
    {
      id: 'topic',
      title: 'Topic Analysis',
      description: 'Question topics and difficulty distribution',
      icon: <PieChart className="h-5 w-5" />,
      color: 'from-orange-600 to-red-600'
    }
  ];

  const performanceData = [
    { date: '2024-01-01', accuracy: 82 },
    { date: '2024-01-02', accuracy: 85 },
    { date: '2024-01-03', accuracy: 78 },
    { date: '2024-01-04', accuracy: 88 },
    { date: '2024-01-05', accuracy: 91 },
    { date: '2024-01-06', accuracy: 87 },
    { date: '2024-01-07', accuracy: 89 }
  ];

  const topicDistribution = [
    { topic: 'React Hooks', percentage: 35, color: 'bg-blue-500' },
    { topic: 'State Management', percentage: 25, color: 'bg-green-500' },
    { topic: 'API Integration', percentage: 20, color: 'bg-purple-500' },
    { topic: 'TypeScript', percentage: 15, color: 'bg-yellow-500' },
    { topic: 'Testing', percentage: 5, color: 'bg-red-500' }
  ];

  const difficultyAnalysis = [
    {
      level: 'Easy',
      total: 168,
      correct: 145,
      incorrect: 23,
      accuracy: 86,
      color: 'from-green-600 to-emerald-600'
    },
    {
      level: 'Medium',
      total: 143,
      correct: 98,
      incorrect: 45,
      accuracy: 69,
      color: 'from-yellow-600 to-orange-600'
    },
    {
      level: 'Hard',
      total: 62,
      correct: 34,
      incorrect: 28,
      accuracy: 55,
      color: 'from-red-600 to-pink-600'
    }
  ];

  const handleGenerateReport = async (templateId: string) => {
    setIsGenerating(true);
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsGenerating(false);
    
    const template = reportTemplates.find(t => t.id === templateId);
    alert(`${template?.title} generated successfully!`);
  };

  const handleExport = async () => {
    setIsGenerating(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsGenerating(false);
    alert(`Data exported as ${selectedFormat} successfully!`);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/50 to-cyan-900/50 backdrop-blur-lg rounded-xl p-6 border border-blue-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <BarChart3 className="h-6 w-6 mr-3 text-blue-400" />
          Generate detailed reports and insights
        </h3>
        <p className="text-blue-200">Comprehensive analytics and reporting dashboard</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Report Templates */}
        <div className="lg:col-span-2 space-y-6">
          {/* Period Selection */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-xl font-semibold text-white flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-purple-400" />
                Report Period
              </h4>
              <button className="p-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                <RefreshCw className="h-4 w-4 text-white" />
              </button>
            </div>
            
            <div className="grid grid-cols-4 gap-3">
              {['7', '30', '90', '365'].map((days) => (
                <button
                  key={days}
                  onClick={() => setSelectedPeriod(days)}
                  className={`p-3 rounded-lg border transition-all ${
                    selectedPeriod === days
                      ? 'bg-purple-600 border-purple-500 text-white'
                      : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  Last {days} Days
                </button>
              ))}
            </div>
          </div>

          {/* Report Templates */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6">Report Templates</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {reportTemplates.map((template) => (
                <div
                  key={template.id}
                  className="bg-white/10 border border-gray-600 rounded-lg p-4 hover:bg-white/20 transition-all cursor-pointer group"
                  onClick={() => handleGenerateReport(template.id)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 bg-gradient-to-r ${template.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                      {template.icon}
                    </div>
                    <div className="flex-1">
                      <h5 className="text-white font-medium mb-1">{template.title}</h5>
                      <p className="text-gray-400 text-sm">{template.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Trends */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
              Performance Trends
            </h4>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm text-gray-400">
                <span>2024-01-01</span>
                <span>2024-01-07</span>
              </div>
              
              <div className="relative h-32 bg-black/40 rounded-lg p-4">
                <div className="flex items-end justify-between h-full">
                  {performanceData.map((data, index) => (
                    <div key={index} className="flex flex-col items-center space-y-2">
                      <div
                        className="w-6 bg-gradient-to-t from-blue-600 to-cyan-400 rounded-t transition-all duration-1000"
                        style={{ height: `${data.accuracy}%` }}
                      ></div>
                      <span className="text-xs text-gray-400">{data.accuracy}%</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-between text-xs text-gray-400">
                <span>0</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
            </div>
          </div>

          {/* Topic Distribution */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
              <PieChart className="h-5 w-5 mr-2 text-purple-400" />
              Topic Distribution
            </h4>
            
            <div className="space-y-4">
              {topicDistribution.map((topic, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">{topic.topic}</span>
                    <span className="text-white font-semibold">{topic.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${topic.color} transition-all duration-1000`}
                      style={{ width: `${topic.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Export Options & Stats */}
        <div className="space-y-6">
          {/* Export Options */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
              <Download className="h-5 w-5 mr-2 text-green-400" />
              Export Options
            </h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Period: Last {selectedPeriod} Days
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Format
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['PDF', 'CSV', 'Excel', 'JSON'].map((format) => (
                    <button
                      key={format}
                      onClick={() => setSelectedFormat(format)}
                      className={`p-2 rounded-lg border text-sm transition-all ${
                        selectedFormat === format
                          ? 'bg-green-600 border-green-500 text-white'
                          : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
                      }`}
                    >
                      {format}
                    </button>
                  ))}
                </div>
              </div>
              
              <button
                onClick={handleExport}
                disabled={isGenerating}
                className="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 text-white rounded-lg transition-all"
              >
                {isGenerating ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span>Export Data</span>
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6">Quick Stats</h4>
            
            <div className="space-y-4">
              <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-blue-300">Total Questions</span>
                  <Target className="h-4 w-4 text-blue-400" />
                </div>
                <p className="text-white text-2xl font-bold">1,247</p>
                <p className="text-green-400 text-sm">+12% from last period</p>
              </div>

              <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-green-300">Avg Accuracy</span>
                  <TrendingUp className="h-4 w-4 text-green-400" />
                </div>
                <p className="text-white text-2xl font-bold">87.5%</p>
                <p className="text-green-400 text-sm">+2.3% improvement</p>
              </div>

              <div className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-purple-300">Active Users</span>
                  <Users className="h-4 w-4 text-purple-400" />
                </div>
                <p className="text-white text-2xl font-bold">342</p>
                <p className="text-green-400 text-sm">+18% growth</p>
              </div>

              <div className="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-yellow-300">Avg Response Time</span>
                  <Clock className="h-4 w-4 text-yellow-400" />
                </div>
                <p className="text-white text-2xl font-bold">2.3s</p>
                <p className="text-green-400 text-sm">-0.5s faster</p>
              </div>
            </div>
          </div>

          {/* Difficulty Analysis */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
              <Activity className="h-5 w-5 mr-2 text-orange-400" />
              Difficulty Analysis
            </h4>
            
            <div className="space-y-4">
              {difficultyAnalysis.map((difficulty, index) => (
                <div key={index} className={`bg-gradient-to-r ${difficulty.color}/20 border border-gray-600 rounded-lg p-4`}>
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="text-white font-medium">{difficulty.level}</h5>
                    <span className="text-white font-bold">{difficulty.accuracy}%</span>
                  </div>
                  <p className="text-gray-300 text-sm mb-2">{difficulty.total} questions</p>
                  <div className="flex justify-between text-xs text-gray-400 mb-2">
                    <span>Correct: {difficulty.correct}</span>
                    <span>Incorrect: {difficulty.incorrect}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full bg-gradient-to-r ${difficulty.color} transition-all duration-1000`}
                      style={{ width: `${difficulty.accuracy}%` }}
                    ></div>
                  </div>
                  <p className="text-center text-xs text-gray-400 mt-1">{difficulty.accuracy}% accuracy</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsAnalytics;
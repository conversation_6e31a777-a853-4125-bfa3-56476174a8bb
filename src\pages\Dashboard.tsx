import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import RoleSwitcher from '../components/RoleSwitcher';
import ClassicDashboard from '../components/ClassicDashboard';
import StudentDashboardViews from '../components/StudentDashboardViews';
import StudentAchievements from '../components/StudentAchievements';
import StudentNotifications from '../components/StudentNotifications';
import StudyMaterials from '../components/StudyMaterials';
import StudentProfile from '../components/StudentProfile';
import CreatePollSession from '../components/CreatePollSession';
import EnhancedAudioCapture from '../components/EnhancedAudioCapture';
import AIQuestionsFeed from '../components/AIQuestionsFeed';
import CreateManualPoll from '../components/CreateManualPoll';
import ParticipantsManagement from '../components/ParticipantsManagement';
import ReportsAnalytics from '../components/ReportsAnalytics';
import SettingsPanel from '../components/SettingsPanel';
import VoiceInput from '../components/VoiceInput';
import LiveTranscription from '../components/LiveTranscription';
import QuestionGenerator from '../components/QuestionGenerator';
import AnalyticsDashboard from '../components/AnalyticsDashboard';
import QuestionReviewModal from '../components/QuestionReviewModal';
import ExportResults from '../components/ExportResults';
import MeetingIntegration from '../components/MeetingIntegration';
import ChatbotAssistant from '../components/ChatbotAssistant';
import LivePoll from '../components/LivePoll';
import PollTimer from '../components/PollTimer';
import { 
  Plus, 
  Users, 
  Zap, 
  BarChart3, 
  Mic, 
  Brain, 
  Trophy,
  Settings,
  FileText,
  Clock,
  Target,
  Activity,
  Sparkles,
  UserCheck,
  MessageCircle,
  Star,
  Bell,
  BookOpen,
  Award,
  User
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, logout, hasPermission } = useAuth();
  const [transcribedText, setTranscribedText] = useState('');
  const [generatedQuestions, setGeneratedQuestions] = useState<any[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [currentPoll, setCurrentPoll] = useState<any>(null);
  const [pollResults, setPollResults] = useState<any>(null);

  if (!user) return null;

  const handleTranscriptionUpdate = (text: string) => {
    setTranscribedText(text);
  };

  const handleQuestionGenerated = (question: any) => {
    setGeneratedQuestions(prev => [...prev, question]);
  };

  const handleQuestionReview = (question: any) => {
    setSelectedQuestion(question);
    setShowReviewModal(true);
  };

  const handlePollSubmit = (selectedOption: number) => {
    const results = {
      selectedOption,
      results: [25, 35, 20, 20],
      correctAnswer: 1,
      totalVotes: 45
    };
    
    setPollResults(results);
    setCurrentPoll(null);
  };

  const handlePollTimeout = () => {
    setCurrentPoll(null);
  };

  const renderMainContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="space-y-8">
            {/* Welcome Header */}
            <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-2xl p-8 border border-purple-500/30 relative overflow-hidden">
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute top-4 right-8 w-4 h-4 bg-purple-400 rounded-full animate-bounce"></div>
                <div className="absolute bottom-6 left-12 w-2 h-2 bg-pink-400 rounded-full animate-ping"></div>
                <div className="absolute top-12 left-20 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              </div>
              
              <div className="relative z-10">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-white mb-2">
                      Welcome back, {user.name}!
                    </h1>
                    <p className="text-purple-200">
                      Current role: <span className="capitalize font-semibold">{user.role}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Role Switcher */}
            <RoleSwitcher />

            {/* Dashboard Views */}
            {hasPermission('create_polls') ? (
              <ClassicDashboard />
            ) : (
              <StudentDashboardViews />
            )}

            {/* Role-specific content */}
            {!hasPermission('create_polls') && (
              // Student Dashboard Content
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
                  <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
                    Your Stats
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Polls Participated</span>
                      <span className="text-white font-semibold">24</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Correct Answers</span>
                      <span className="text-green-400 font-semibold">21</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Accuracy</span>
                      <span className="text-blue-400 font-semibold">87%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Current Rank</span>
                      <span className="text-yellow-400 font-semibold">#5</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
                  <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2 text-blue-400" />
                    Recent Activity
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-600">
                      <span className="text-gray-300 text-sm">React Fundamentals Quiz</span>
                      <span className="text-green-400 text-xs">95%</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-600">
                      <span className="text-gray-300 text-sm">JavaScript ES6 Challenge</span>
                      <span className="text-green-400 text-xs">88%</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-600">
                      <span className="text-gray-300 text-sm">CSS Grid Layout Quiz</span>
                      <span className="text-yellow-400 text-xs">76%</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
                  <h3 className="text-xl font-semibold text-white mb-4">Quick Actions</h3>
                  <div className="space-y-3">
                    <button 
                      onClick={() => setActiveSection('join-poll')}
                      className="w-full p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      Join Active Poll
                    </button>
                    <button 
                      onClick={() => setActiveSection('leaderboard')}
                      className="w-full p-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                    >
                      View Leaderboard
                    </button>
                    <button 
                      onClick={() => setActiveSection('achievements')}
                      className="w-full p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      My Achievements
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'create-session':
        if (!hasPermission('create_polls')) {
          return <div className="text-white">Access denied. Host role required.</div>;
        }
        return <CreatePollSession />;

      case 'audio-capture':
        if (!hasPermission('create_polls')) {
          return <div className="text-white">Access denied. Host role required.</div>;
        }
        return (
          <EnhancedAudioCapture 
            onTranscriptionUpdate={handleTranscriptionUpdate}
            isRecording={isRecording}
            setIsRecording={setIsRecording}
          />
        );

      case 'ai-questions':
        if (!hasPermission('create_polls')) {
          return <div className="text-white">Access denied. Host role required.</div>;
        }
        return <AIQuestionsFeed />;

      case 'manual-poll':
        if (!hasPermission('create_polls')) {
          return <div className="text-white">Access denied. Host role required.</div>;
        }
        return <CreateManualPoll />;

      case 'participants':
        return <ParticipantsManagement />;

      case 'join-poll':
      case 'active-polls':
        return (
          <div className="max-w-4xl mx-auto">
            {currentPoll ? (
              <div className="space-y-6">
                <PollTimer 
                  timeLimit={currentPoll.timeLimit}
                  onTimeout={handlePollTimeout}
                />
                <LivePoll 
                  poll={currentPoll}
                  onSubmit={handlePollSubmit}
                />
              </div>
            ) : pollResults ? (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
                <h3 className="text-2xl font-semibold text-white mb-6">Poll Results</h3>
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <p className="text-green-400 text-lg font-semibold">
                      {pollResults.selectedOption === pollResults.correctAnswer ? 'Correct!' : 'Incorrect'}
                    </p>
                    <p className="text-gray-300">Total votes: {pollResults.totalVotes}</p>
                  </div>
                  
                  {pollResults.results.map((percentage: number, index: number) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className={`text-sm ${
                          index === pollResults.correctAnswer ? 'text-green-400' : 'text-gray-300'
                        }`}>
                          Option {index + 1} {index === pollResults.correctAnswer && '(Correct)'}
                        </span>
                        <span className="text-sm text-gray-300">{percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-1000 ${
                            index === pollResults.correctAnswer ? 'bg-green-500' : 'bg-purple-500'
                          }`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button 
                  onClick={() => setPollResults(null)}
                  className="w-full mt-6 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Ready for Next Poll
                </button>
              </div>
            ) : (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-12 text-center">
                <Clock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-2xl font-semibold text-white mb-2">Waiting for Poll</h3>
                <p className="text-gray-300">
                  The host will start a poll shortly. Stay tuned!
                </p>
                <div className="mt-6">
                  <div className="inline-flex items-center space-x-2 text-gray-400">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'leaderboard':
        return <AnalyticsDashboard />;

      case 'achievements':
        return <StudentAchievements />;

      case 'notifications':
        return <StudentNotifications />;

      case 'study-materials':
        return <StudyMaterials />;

      case 'profile':
        return <StudentProfile />;

      case 'reports':
        if (!hasPermission('export_data')) {
          return <div className="text-white">Access denied. Host role required.</div>;
        }
        return <ReportsAnalytics />;

      case 'settings':
        return <SettingsPanel />;

      case 'my-stats':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
                Performance Overview
              </h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Overall Accuracy</span>
                    <span className="text-blue-400 font-semibold">87%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Response Speed</span>
                    <span className="text-green-400 font-semibold">Fast</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Recent Achievements</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-2 bg-yellow-600/20 border border-yellow-500/30 rounded-lg">
                  <Trophy className="h-5 w-5 text-yellow-400" />
                  <span className="text-yellow-300 text-sm">Speed Demon - 5 quick answers</span>
                </div>
                <div className="flex items-center space-x-3 p-2 bg-green-600/20 border border-green-500/30 rounded-lg">
                  <Trophy className="h-5 w-5 text-green-400" />
                  <span className="text-green-300 text-sm">Accuracy Master - 80% correct</span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'poll-history':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-purple-400" />
              Poll History
            </h3>
            <p className="text-gray-300">Your poll history will appear here...</p>
          </div>
        );

      case 'help':
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Help & Support</h3>
            <p className="text-gray-300">Help documentation coming soon...</p>
          </div>
        );

      default:
        return (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Dashboard</h3>
            <p className="text-gray-300">Welcome to your dashboard!</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      <Header />
      
      <div className="flex flex-1">
        <Sidebar 
          userRole={hasPermission('create_polls') ? 'host' : 'participant'}
          activeSection={activeSection}
          onSectionChange={setActiveSection}
          onLogout={logout}
        />
        
        <main className="flex-1 p-8 overflow-y-auto bg-gradient-to-br from-black via-purple-900/20 to-black">
          {renderMainContent()}
        </main>
      </div>

      {/* Question Review Modal */}
      {showReviewModal && selectedQuestion && (
        <QuestionReviewModal
          question={selectedQuestion}
          onClose={() => setShowReviewModal(false)}
          onSave={(updatedQuestion) => {
            setShowReviewModal(false);
          }}
        />
      )}

      <ChatbotAssistant />
    </div>
  );
};

export default Dashboard;
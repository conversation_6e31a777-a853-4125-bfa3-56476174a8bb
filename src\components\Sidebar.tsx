import React, { useState } from 'react';
import { 
  Home, 
  Mic, 
  Brain, 
  BarChart3, 
  Download, 
  Video, 
  Settings, 
  HelpCircle,
  Users,
  Trophy,
  Clock,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  Plus,
  FileText,
  LogOut,
  Zap,
  Activity,
  Star,
  Bell,
  BookOpen,
  Award,
  User,
  Target
} from 'lucide-react';

interface SidebarProps {
  userRole: 'host' | 'participant';
  activeSection: string;
  onSectionChange: (section: string) => void;
  onLogout?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ userRole, activeSection, onSectionChange, onLogout }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const hostSections = [
    { id: 'dashboard', label: 'Home Dashboard', icon: Home },
    { id: 'create-session', label: 'Create Poll Session', icon: Plus },
    { id: 'audio-capture', label: 'Audio Capture', icon: Mic },
    { id: 'ai-questions', label: 'AI Questions', icon: Brain },
    { id: 'manual-poll', label: 'Create Manual Poll', icon: FileText },
    { id: 'participants', label: 'Participants', icon: Users },
    { id: 'leaderboard', label: 'Leaderboard', icon: Trophy },
    { id: 'reports', label: 'Reports', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const participantSections = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'join-poll', label: 'Join Poll', icon: Target },
    { id: 'leaderboard', label: 'Leaderboard', icon: Trophy },
    { id: 'achievements', label: 'Achievements', icon: Award },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'study-materials', label: 'Study Materials', icon: BookOpen },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'poll-history', label: 'Poll History', icon: Clock },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'help', label: 'Help & Support', icon: HelpCircle }
  ];

  const sections = userRole === 'host' ? hostSections : participantSections;

  return (
    <div className={`bg-black/80 backdrop-blur-lg border-r border-purple-500/30 transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } h-full flex flex-col relative overflow-hidden`}>
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-4 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-6 w-1 h-1 bg-pink-400 rounded-full animate-ping"></div>
        <div className="absolute bottom-40 left-8 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
        <div className="absolute top-64 left-12 w-1 h-1 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 right-4 w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Header */}
      <div className="p-4 border-b border-purple-500/30 relative z-10">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                <h2 className="text-lg font-bold text-white capitalize">{userRole} Panel</h2>
              </div>
              <p className="text-sm text-purple-300">
                {userRole === 'host' ? 'AI Control Center' : 'Learning Dashboard'}
              </p>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 hover:bg-purple-500/20 rounded-lg transition-colors group"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4 text-purple-300 group-hover:text-white transition-colors" />
            ) : (
              <ChevronLeft className="h-4 w-4 text-purple-300 group-hover:text-white transition-colors" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 relative z-10">
        <div className="space-y-2">
          {sections.map((section, index) => {
            const Icon = section.icon;
            const isActive = activeSection === section.id;
            
            return (
              <button
                key={section.id}
                onClick={() => onSectionChange(section.id)}
                className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group relative overflow-hidden ${
                  isActive 
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25' 
                    : 'text-gray-300 hover:bg-purple-500/20 hover:text-white'
                }`}
                title={isCollapsed ? section.label : undefined}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Animated background for active item */}
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 animate-pulse"></div>
                )}
                
                <Icon className={`h-5 w-5 flex-shrink-0 transition-transform duration-200 ${
                  isActive ? 'scale-110' : 'group-hover:scale-105'
                }`} />
                {!isCollapsed && (
                  <span className="font-medium transition-all duration-200">{section.label}</span>
                )}
                
                {/* Active indicator */}
                {isActive && !isCollapsed && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                )}
              </button>
            );
          })}
        </div>
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-purple-500/30 relative z-10">
        <button
          onClick={onLogout}
          className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 text-red-300 hover:bg-red-500/20 hover:text-red-200 group ${
            isCollapsed ? 'justify-center' : ''
          }`}
          title={isCollapsed ? 'Logout' : undefined}
        >
          <LogOut className="h-5 w-5 flex-shrink-0 group-hover:scale-105 transition-transform duration-200" />
          {!isCollapsed && (
            <span className="font-medium">Logout</span>
          )}
        </button>
      </div>

      {/* Status Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t border-purple-500/30 relative z-10">
          <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-3 relative overflow-hidden">
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 animate-pulse"></div>
            
            <div className="relative z-10">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-white">
                  {userRole === 'host' ? 'AI Engine Active' : 'Learning Mode'}
                </span>
              </div>
              <p className="text-xs text-purple-200">
                {userRole === 'host' ? 'All systems operational' : 'Ready to learn'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
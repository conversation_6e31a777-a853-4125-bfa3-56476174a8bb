import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ff, 
  Check, 
  X, 
  Clock, 
  Trophy, 
  Users, 
  MessageCircle,
  AlertTriangle,
  Info,
  Star,
  Zap,
  Settings
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'poll' | 'achievement' | 'system' | 'social' | 'reminder';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionRequired?: boolean;
}

const StudentNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'poll',
      title: 'New Poll Available',
      message: 'React Fundamentals Quiz is now live! Join now to participate.',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      read: false,
      priority: 'high',
      actionRequired: true
    },
    {
      id: '2',
      type: 'achievement',
      title: 'Achievement Unlocked!',
      message: 'Congratulations! You earned "Speed Demon" for quick responses.',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      priority: 'medium'
    },
    {
      id: '3',
      type: 'system',
      title: 'System Maintenance',
      message: 'Scheduled maintenance tonight from 2-4 AM. Service may be interrupted.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: true,
      priority: 'medium'
    },
    {
      id: '4',
      type: 'social',
      title: 'Leaderboard Update',
      message: 'You moved up to #5 in the weekly leaderboard! Keep it up!',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: false,
      priority: 'low'
    },
    {
      id: '5',
      type: 'reminder',
      title: 'Study Streak Reminder',
      message: 'Don\'t break your 12-day streak! Join a poll today.',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      read: true,
      priority: 'medium'
    }
  ]);

  const [notificationSettings, setNotificationSettings] = useState({
    polls: true,
    achievements: true,
    system: true,
    social: false,
    reminders: true,
    sound: true,
    desktop: true
  });

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'poll': return <MessageCircle className="h-5 w-5 text-blue-400" />;
      case 'achievement': return <Trophy className="h-5 w-5 text-yellow-400" />;
      case 'system': return <Settings className="h-5 w-5 text-gray-400" />;
      case 'social': return <Users className="h-5 w-5 text-green-400" />;
      case 'reminder': return <Clock className="h-5 w-5 text-purple-400" />;
      default: return <Bell className="h-5 w-5 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500/30 bg-red-600/10';
      case 'medium': return 'border-yellow-500/30 bg-yellow-600/10';
      case 'low': return 'border-gray-500/30 bg-gray-600/10';
      default: return 'border-gray-500/30 bg-gray-600/10';
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 backdrop-blur-lg rounded-xl p-6 border border-blue-500/30">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-2xl font-semibold text-white flex items-center">
            <Bell className="h-6 w-6 mr-3 text-blue-400" />
            Notifications
          </h3>
          <div className="flex items-center space-x-3">
            {unreadCount > 0 && (
              <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {unreadCount} new
              </span>
            )}
            <button
              onClick={markAllAsRead}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm"
            >
              Mark all read
            </button>
          </div>
        </div>
        <p className="text-blue-200">Stay updated with polls, achievements, and important announcements</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Notifications List */}
        <div className="lg:col-span-2 space-y-4">
          {notifications.length === 0 ? (
            <div className="bg-black/60 backdrop-blur-lg rounded-xl p-12 border border-purple-500/30 text-center">
              <BellOff className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-white text-xl font-semibold mb-2">No notifications</h4>
              <p className="text-gray-400">You're all caught up! Check back later for updates.</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-black/60 backdrop-blur-lg rounded-xl p-6 border transition-all hover:bg-black/80 ${
                  notification.read ? 'opacity-60' : 'opacity-100'
                } ${getPriorityColor(notification.priority)}`}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className={`font-semibold ${notification.read ? 'text-gray-300' : 'text-white'}`}>
                        {notification.title}
                      </h4>
                      <div className="flex items-center space-x-2 ml-4">
                        <span className="text-xs text-gray-400">
                          {formatTimestamp(notification.timestamp)}
                        </span>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                    
                    <p className={`text-sm mb-3 ${notification.read ? 'text-gray-400' : 'text-gray-300'}`}>
                      {notification.message}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full capitalize ${
                          notification.priority === 'high' ? 'bg-red-600/20 text-red-300' :
                          notification.priority === 'medium' ? 'bg-yellow-600/20 text-yellow-300' :
                          'bg-gray-600/20 text-gray-300'
                        }`}>
                          {notification.priority}
                        </span>
                        <span className="text-xs text-gray-500 capitalize">{notification.type}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {notification.actionRequired && (
                          <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors">
                            Take Action
                          </button>
                        )}
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="p-1 text-gray-400 hover:text-white transition-colors"
                            title="Mark as read"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                          title="Delete"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Notification Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Settings className="h-5 w-5 mr-2 text-purple-400" />
            Notification Settings
          </h4>

          <div className="space-y-4">
            <div>
              <h5 className="text-white font-medium mb-3">Notification Types</h5>
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="h-4 w-4 text-blue-400" />
                    <span className="text-gray-300 text-sm">Poll Notifications</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.polls}
                    onChange={(e) => setNotificationSettings({...notificationSettings, polls: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Trophy className="h-4 w-4 text-yellow-400" />
                    <span className="text-gray-300 text-sm">Achievements</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.achievements}
                    onChange={(e) => setNotificationSettings({...notificationSettings, achievements: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Settings className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300 text-sm">System Updates</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.system}
                    onChange={(e) => setNotificationSettings({...notificationSettings, system: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Users className="h-4 w-4 text-green-400" />
                    <span className="text-gray-300 text-sm">Social Updates</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.social}
                    onChange={(e) => setNotificationSettings({...notificationSettings, social: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-purple-400" />
                    <span className="text-gray-300 text-sm">Reminders</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={notificationSettings.reminders}
                    onChange={(e) => setNotificationSettings({...notificationSettings, reminders: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-600">
              <h5 className="text-white font-medium mb-3">Delivery Options</h5>
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Sound Notifications</span>
                  <input
                    type="checkbox"
                    checked={notificationSettings.sound}
                    onChange={(e) => setNotificationSettings({...notificationSettings, sound: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Desktop Notifications</span>
                  <input
                    type="checkbox"
                    checked={notificationSettings.desktop}
                    onChange={(e) => setNotificationSettings({...notificationSettings, desktop: e.target.checked})}
                    className="w-4 h-4 text-purple-600 rounded"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentNotifications;
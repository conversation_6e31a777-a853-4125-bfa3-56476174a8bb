import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server } from 'socket.io';
import config from './config/env.js';
import connectDB from './config/database.js';
import cacheService from './services/cacheService.js';
import performanceService from './services/performanceService.js';
import dbOptimization from './services/databaseOptimization.js';
import logger, { logUnhandledError, logUnhandledRejection } from './services/logger.js';
import authRoutes from './routes/auth.js';
import pollRoutes from './routes/polls.js';
import userRoutes from './routes/users.js';
import sessionRoutes from './routes/sessions.js';
import uploadRoutes from './routes/upload.js';
import aiRoutes from './routes/ai.js';
import analyticsRoutes from './routes/analytics.js';
import { errorHand<PERSON>, notFoundHandler } from './middleware/errorHandler.js';
import { loggingMiddleware } from './middleware/requestLogger.js';
import { setupSwagger } from './config/swagger.js';
import { setupSocketHandlers } from './socket/socketHandlers.js';
import rateLimit from 'express-rate-limit';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: config.CLIENT_URL,
    methods: ["GET", "POST"]
  }
});

// Global error handlers
process.on('uncaughtException', (error: Error) => {
  logUnhandledError(error, { type: 'uncaughtException' });
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logUnhandledRejection(reason, promise);
  console.error('Unhandled Rejection:', reason);
  process.exit(1);
});

// Connect to MongoDB and initialize optimization
connectDB().then(async () => {
  logger.info('Database connected successfully');

  // Initialize database optimization
  await dbOptimization.createIndexes();
  dbOptimization.optimizeConnectionPool();
  dbOptimization.startPeriodicMaintenance();

  // Start performance monitoring
  performanceService.startPeriodicCleanup();

  logger.info('Server optimization services initialized');
}).catch((error) => {
  logger.error('Failed to connect to database', error);

  // In development, continue without database
  if (process.env.NODE_ENV === 'development') {
    console.log('⚠️  Continuing in development mode without database...');
    logger.info('Server running without database connection in development mode');
  } else {
    process.exit(1);
  }
});

// Performance monitoring middleware
app.use(performanceService.trackRequest());

// Security middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_MAX_REQUESTS
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: config.CORS_ORIGIN,
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
// app.use(loggingMiddleware); // Temporarily disabled

// Static file serving for uploads
app.use('/uploads', express.static('uploads'));

// Setup Swagger documentation
setupSwagger(app);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/polls', pollRoutes);
app.use('/api/users', userRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/analytics', analyticsRoutes);

// Performance monitoring endpoint
app.get('/api/health', async (req, res) => {
  try {
    const performanceStats = performanceService.getStats(60); // Last 60 minutes
    const systemHealth = performanceService.getSystemHealth();
    const dbHealth = await dbOptimization.healthCheck();
    const cacheHealth = await cacheService.healthCheck();

    logger.info('Health check requested', { ip: req.ip });

    res.json({
      success: true,
      timestamp: new Date(),
      uptime: process.uptime(),
      performance: performanceStats,
      system: systemHealth,
      database: dbHealth,
      cache: {
        connected: cacheHealth,
        enabled: !!config.REDIS_URL
      }
    });
  } catch (error) {
    logger.error('Health check failed', error);
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Socket.IO setup
setupSocketHandlers(io);

// 404 handler for undefined routes
app.use(notFoundHandler);

// Error handling middleware
app.use(errorHandler);

// Handle port conflicts gracefully
const startServer = (port: number) => {
  server.listen(port, () => {
    logger.info(`Server started successfully`, {
      port: port,
      environment: config.NODE_ENV,
      clientUrl: config.CLIENT_URL,
      timestamp: new Date().toISOString()
    });

    console.log(`🚀 Server running on port ${port}`);
    console.log(`🌍 Environment: ${config.NODE_ENV}`);
    console.log(`🔗 Client URL: ${config.CLIENT_URL}`);
  }).on('error', (err: any) => {
    if (err.code === 'EADDRINUSE') {
      console.log(`⚠️  Port ${port} is busy, trying port ${port + 1}...`);
      startServer(port + 1);
    } else {
      console.error('❌ Server failed to start:', err);
      process.exit(1);
    }
  });
};

startServer(config.PORT);

export { io };
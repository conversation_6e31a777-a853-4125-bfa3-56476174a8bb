import React, { useEffect, useRef } from 'react';
import { FileText, Volume2, Copy, Download } from 'lucide-react';

interface LiveTranscriptionProps {
  text: string;
}

const LiveTranscription: React.FC<LiveTranscriptionProps> = ({ text }) => {
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (textRef.current) {
      textRef.current.scrollTop = textRef.current.scrollHeight;
    }
  }, [text]);

  const handleSpeak = () => {
    if ('speechSynthesis' in window && text) {
      window.speechSynthesis.speak(new SpeechSynthesisUtterance(text));
    }
  };

  const handleCopy = () => {
    if (text) {
      navigator.clipboard.writeText(text);
    }
  };

  const handleDownload = () => {
    if (text) {
      const blob = new Blob([text], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcription-${Date.now()}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-6 right-4 w-1 h-1 bg-blue-400 rounded-full animate-ping"></div>
        <div className="absolute bottom-8 left-6 w-1.5 h-1.5 bg-green-400 rounded-full animate-bounce"></div>
        <div className="absolute top-16 left-8 w-1 h-1 bg-yellow-400 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-400" />
            Live AI Transcription
          </h3>
          {text && (
            <div className="flex items-center space-x-2">
              <button
                onClick={handleCopy}
                className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                title="Copy text"
              >
                <Copy className="h-4 w-4 text-white" />
              </button>
              <button
                onClick={handleDownload}
                className="p-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                title="Download transcription"
              >
                <Download className="h-4 w-4 text-white" />
              </button>
              <button
                onClick={handleSpeak}
                className="p-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                title="Read aloud"
              >
                <Volume2 className="h-4 w-4 text-white" />
              </button>
            </div>
          )}
        </div>

        <div 
          ref={textRef}
          className="h-40 bg-black/40 rounded-lg p-4 overflow-y-auto border border-purple-500/30 relative"
        >
          {text ? (
            <div className="space-y-2">
              <p className="text-gray-200 leading-relaxed">
                {text}
              </p>
              {/* Typing indicator */}
              <div className="flex items-center space-x-1">
                <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <p className="text-gray-400 italic">
                  AI transcription will appear here...
                </p>
                <p className="text-gray-500 text-sm mt-1">
                  Start recording to see live text conversion
                </p>
              </div>
            </div>
          )}
        </div>

        {text && (
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Words</p>
              <p className="text-white font-semibold">{text.split(' ').length}</p>
            </div>
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Characters</p>
              <p className="text-white font-semibold">{text.length}</p>
            </div>
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Confidence</p>
              <p className="text-white font-semibold">98%</p>
            </div>
          </div>
        )}

        {/* AI Processing Indicator */}
        {text && (
          <div className="mt-4 flex items-center justify-center space-x-2 text-xs text-gray-400">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <span>AI processing in real-time</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveTranscription;
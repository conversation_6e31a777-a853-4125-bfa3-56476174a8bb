import React, { useState } from 'react';
import { 
  Setting<PERSON>, 
  User, 
  Camera, 
  Bell, 
  Mic, 
  <PERSON>, 
  <PERSON>, 
  Palette, 
  Brain,
  Upload,
  Save,
  RefreshCw
} from 'lucide-react';

const SettingsPanel: React.FC = () => {
  const [profileData, setProfileData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    bio: 'Computer Science student passionate about learning'
  });

  const [generalSettings, setGeneralSettings] = useState({
    defaultTimer: 30,
    autoLaunch: true,
    notifications: true
  });

  const [audioSettings, setAudioSettings] = useState({
    microphoneDevice: 'Default Microphone',
    microphoneVolume: 75,
    audioFeedback: true
  });

  const [securitySettings, setSecuritySettings] = useState({
    screenshotDetection: true,
    copyProtection: false,
    blurOnFocusLoss: true,
    sessionTimeout: 60
  });

  const [themeSettings, setThemeSettings] = useState({
    darkMode: true,
    colorPreset: 'Purple',
    fontSize: 'Medium'
  });

  const [aiSettings, setAiSettings] = useState({
    confidenceThreshold: 80,
    autoApprove: true,
    smartFiltering: true
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsSaving(false);
    alert('Settings saved successfully!');
  };

  const colorPresets = [
    { name: 'Purple', color: 'bg-purple-500' },
    { name: 'Blue', color: 'bg-blue-500' },
    { name: 'Green', color: 'bg-green-500' },
    { name: 'Orange', color: 'bg-orange-500' }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <Settings className="h-6 w-6 mr-3 text-purple-400" />
          Settings & Preferences
        </h3>
        <p className="text-purple-200">Customize your polling experience</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Personal Information */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <User className="h-5 w-5 mr-2 text-blue-400" />
            Personal Information
          </h4>

          <div className="space-y-6">
            {/* Profile Picture */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">Profile Picture</label>
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <User className="h-8 w-8 text-white" />
                </div>
                <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                  <Upload className="h-4 w-4" />
                  <span>Upload new picture</span>
                </button>
              </div>
            </div>

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">First Name</label>
                <input
                  type="text"
                  value={profileData.firstName}
                  onChange={(e) => setProfileData({...profileData, firstName: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
                <input
                  type="text"
                  value={profileData.lastName}
                  onChange={(e) => setProfileData({...profileData, lastName: e.target.value})}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
              <input
                type="email"
                value={profileData.email}
                onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
              <textarea
                value={profileData.bio}
                onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                rows={3}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              />
            </div>
          </div>
        </div>

        {/* General Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Settings className="h-5 w-5 mr-2 text-green-400" />
            General Settings
          </h4>

          <div className="space-y-6">
            {/* Default Timer */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Default Timer Duration: {generalSettings.defaultTimer}s
              </label>
              <input
                type="range"
                min="10"
                max="300"
                value={generalSettings.defaultTimer}
                onChange={(e) => setGeneralSettings({...generalSettings, defaultTimer: Number(e.target.value)})}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>10s</span>
                <span>5min</span>
              </div>
            </div>

            {/* Toggle Settings */}
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <RefreshCw className="h-4 w-4 text-purple-400" />
                  <span className="text-gray-300">Enable Auto-Launch</span>
                </div>
                <input
                  type="checkbox"
                  checked={generalSettings.autoLaunch}
                  onChange={(e) => setGeneralSettings({...generalSettings, autoLaunch: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Automatically launch approved questions</p>

              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Bell className="h-4 w-4 text-blue-400" />
                  <span className="text-gray-300">Enable Notifications</span>
                </div>
                <input
                  type="checkbox"
                  checked={generalSettings.notifications}
                  onChange={(e) => setGeneralSettings({...generalSettings, notifications: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Receive system notifications</p>
            </div>
          </div>
        </div>

        {/* Audio Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Mic className="h-5 w-5 mr-2 text-yellow-400" />
            Audio Settings
          </h4>

          <div className="space-y-6">
            {/* Microphone Device */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Microphone Device</label>
              <select
                value={audioSettings.microphoneDevice}
                onChange={(e) => setAudioSettings({...audioSettings, microphoneDevice: e.target.value})}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option>Default Microphone</option>
                <option>USB Microphone</option>
                <option>Bluetooth Headset</option>
                <option>Internal Microphone</option>
              </select>
            </div>

            {/* Microphone Volume */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Microphone Volume: {audioSettings.microphoneVolume}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={audioSettings.microphoneVolume}
                onChange={(e) => setAudioSettings({...audioSettings, microphoneVolume: Number(e.target.value)})}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Audio Feedback */}
            <label className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mic className="h-4 w-4 text-green-400" />
                <span className="text-gray-300">Audio Feedback</span>
              </div>
              <input
                type="checkbox"
                checked={audioSettings.audioFeedback}
                onChange={(e) => setAudioSettings({...audioSettings, audioFeedback: e.target.checked})}
                className="w-4 h-4 text-purple-600 rounded"
              />
            </label>
            <p className="text-xs text-gray-400">Play sounds for interactions</p>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Shield className="h-5 w-5 mr-2 text-red-400" />
            Security Settings
          </h4>

          <div className="space-y-6">
            {/* Security Toggles */}
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Camera className="h-4 w-4 text-red-400" />
                  <span className="text-gray-300">Screenshot Detection</span>
                </div>
                <input
                  type="checkbox"
                  checked={securitySettings.screenshotDetection}
                  onChange={(e) => setSecuritySettings({...securitySettings, screenshotDetection: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Detect screenshot attempts</p>

              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Shield className="h-4 w-4 text-orange-400" />
                  <span className="text-gray-300">Copy Protection</span>
                </div>
                <input
                  type="checkbox"
                  checked={securitySettings.copyProtection}
                  onChange={(e) => setSecuritySettings({...securitySettings, copyProtection: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Prevent text copying</p>

              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Eye className="h-4 w-4 text-blue-400" />
                  <span className="text-gray-300">Blur on Focus Loss</span>
                </div>
                <input
                  type="checkbox"
                  checked={securitySettings.blurOnFocusLoss}
                  onChange={(e) => setSecuritySettings({...securitySettings, blurOnFocusLoss: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Blur screen when window loses focus</p>
            </div>

            {/* Session Timeout */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Session Timeout: {securitySettings.sessionTimeout}m
              </label>
              <input
                type="range"
                min="15"
                max="240"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: Number(e.target.value)})}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>15m</span>
                <span>4h</span>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Palette className="h-5 w-5 mr-2 text-pink-400" />
            Theme Settings
          </h4>

          <div className="space-y-6">
            {/* Dark Mode */}
            <label className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Palette className="h-4 w-4 text-purple-400" />
                <span className="text-gray-300">Dark Mode</span>
              </div>
              <input
                type="checkbox"
                checked={themeSettings.darkMode}
                onChange={(e) => setThemeSettings({...themeSettings, darkMode: e.target.checked})}
                className="w-4 h-4 text-purple-600 rounded"
              />
            </label>
            <p className="text-xs text-gray-400">Toggle between light and dark themes</p>

            {/* Color Presets */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">Color Presets</label>
              <div className="grid grid-cols-4 gap-3">
                {colorPresets.map((preset) => (
                  <button
                    key={preset.name}
                    onClick={() => setThemeSettings({...themeSettings, colorPreset: preset.name})}
                    className={`p-3 rounded-lg border transition-all ${
                      themeSettings.colorPreset === preset.name
                        ? 'border-white bg-white/10'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <div className={`w-6 h-6 ${preset.color} rounded-full mx-auto mb-1`}></div>
                    <span className="text-xs text-gray-300">{preset.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Font Size */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Font Size</label>
              <select
                value={themeSettings.fontSize}
                onChange={(e) => setThemeSettings({...themeSettings, fontSize: e.target.value})}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option>Small</option>
                <option>Medium</option>
                <option>Large</option>
              </select>
            </div>
          </div>
        </div>

        {/* AI & Automation Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Brain className="h-5 w-5 mr-2 text-cyan-400" />
            AI & Automation Settings
          </h4>

          <div className="space-y-6">
            {/* AI Confidence Threshold */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                AI Confidence Threshold: {aiSettings.confidenceThreshold}%
              </label>
              <input
                type="range"
                min="50"
                max="100"
                value={aiSettings.confidenceThreshold}
                onChange={(e) => setAiSettings({...aiSettings, confidenceThreshold: Number(e.target.value)})}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <p className="text-xs text-gray-400 mt-1">Minimum confidence for AI-generated questions</p>
            </div>

            {/* AI Toggles */}
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Brain className="h-4 w-4 text-green-400" />
                  <span className="text-gray-300">Auto-approve High Confidence</span>
                </div>
                <input
                  type="checkbox"
                  checked={aiSettings.autoApprove}
                  onChange={(e) => setAiSettings({...aiSettings, autoApprove: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Automatically approve questions above threshold</p>

              <label className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Brain className="h-4 w-4 text-blue-400" />
                  <span className="text-gray-300">Smart Filtering</span>
                </div>
                <input
                  type="checkbox"
                  checked={aiSettings.smartFiltering}
                  onChange={(e) => setAiSettings({...aiSettings, smartFiltering: e.target.checked})}
                  className="w-4 h-4 text-purple-600 rounded"
                />
              </label>
              <p className="text-xs text-gray-400 ml-7">Use AI to filter duplicate questions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          disabled={isSaving}
          className="flex items-center space-x-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 text-white px-8 py-3 rounded-lg transition-all transform hover:scale-105 font-semibold"
        >
          {isSaving ? (
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          ) : (
            <Save className="h-5 w-5" />
          )}
          <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
        </button>
      </div>
    </div>
  );
};

export default SettingsPanel;
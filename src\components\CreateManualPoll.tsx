import React, { useState } from 'react';
import { Plus, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Zap, Save, Play } from 'lucide-react';

const CreateManualPoll: React.FC = () => {
  const [pollQuestion, setPollQuestion] = useState('');
  const [questionType, setQuestionType] = useState('multiple-choice');
  const [options, setOptions] = useState(['', '', '', '']);
  const [correctAnswer, setCorrectAnswer] = useState(0);
  const [timerEnabled, setTimerEnabled] = useState(true);
  const [timerDuration, setTimerDuration] = useState(30);
  const [category, setCategory] = useState('General');
  const [difficulty, setDifficulty] = useState('Medium');

  const questionTypes = [
    { id: 'multiple-choice', name: 'Multiple Choice', description: 'A, B, C, D options' },
    { id: 'true-false', name: 'True/False', description: 'Yes or No question' },
    { id: 'short-answer', name: 'Short Answer', description: 'Text response' },
    { id: 'opinion-poll', name: 'Opinion Poll', description: 'Rating scale' }
  ];

  const updateOption = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const addOption = () => {
    if (options.length < 6) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index: number) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
      if (correctAnswer >= newOptions.length) {
        setCorrectAnswer(0);
      }
    }
  };

  const renderPreview = () => {
    if (!pollQuestion) return null;

    return (
      <div className="bg-black/40 border border-purple-500/30 rounded-lg p-6">
        <h4 className="text-white font-medium mb-4 flex items-center">
          <Eye className="h-4 w-4 mr-2" />
          Preview
        </h4>
        
        <div className="bg-white/10 backdrop-blur-lg rounded-lg p-6">
          <h5 className="text-xl font-semibold text-white mb-4">{pollQuestion}</h5>
          
          {questionType === 'multiple-choice' && (
            <div className="space-y-3">
              {options.filter(opt => opt.trim()).map((option, index) => (
                <button
                  key={index}
                  className={`w-full p-3 text-left rounded-lg border transition-all ${
                    index === correctAnswer
                      ? 'bg-green-600/20 border-green-500/30 text-green-200'
                      : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  <span className="font-medium">
                    {String.fromCharCode(65 + index)}. {option}
                  </span>
                </button>
              ))}
            </div>
          )}
          
          {questionType === 'true-false' && (
            <div className="grid grid-cols-2 gap-3">
              <button className="p-3 bg-white/10 border border-gray-600 text-gray-300 rounded-lg hover:bg-white/20 transition-all">
                True
              </button>
              <button className="p-3 bg-white/10 border border-gray-600 text-gray-300 rounded-lg hover:bg-white/20 transition-all">
                False
              </button>
            </div>
          )}
          
          {questionType === 'short-answer' && (
            <textarea
              placeholder="Type your answer here..."
              className="w-full p-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={3}
            />
          )}
          
          {questionType === 'opinion-poll' && (
            <div className="space-y-3">
              <div className="flex justify-between text-sm text-gray-300">
                <span>Strongly Disagree</span>
                <span>Strongly Agree</span>
              </div>
              <div className="flex space-x-2">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    className="flex-1 p-3 bg-white/10 border border-gray-600 text-gray-300 rounded-lg hover:bg-purple-600/20 transition-all"
                  >
                    {rating}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {timerEnabled && (
            <div className="mt-4 flex items-center justify-center space-x-2 text-gray-400">
              <Clock className="h-4 w-4" />
              <span className="text-sm">Time limit: {timerDuration} seconds</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/50 to-cyan-900/50 backdrop-blur-lg rounded-2xl p-8 border border-blue-500/30">
        <h2 className="text-3xl font-bold text-white mb-4 flex items-center">
          <Plus className="h-8 w-8 mr-3 text-blue-400" />
          Create Manual Poll
        </h2>
        <p className="text-blue-200">Design and launch your custom poll question</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Poll Creation Form */}
        <div className="space-y-6">
          {/* Poll Question */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4">Poll Question</h3>
            <textarea
              value={pollQuestion}
              onChange={(e) => setPollQuestion(e.target.value)}
              placeholder="Type your question here..."
              className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={3}
            />
          </div>

          {/* Question Type */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4">Question Type</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {questionTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setQuestionType(type.id)}
                  className={`p-4 rounded-lg border transition-all text-left ${
                    questionType === type.id
                      ? 'bg-purple-600 border-purple-500 text-white'
                      : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  <div className="font-medium">{type.name}</div>
                  <div className="text-sm opacity-75">{type.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Answer Options */}
          {questionType === 'multiple-choice' && (
            <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white">Answer Options</h3>
                <button
                  onClick={addOption}
                  disabled={options.length >= 6}
                  className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  <Plus className="h-3 w-3" />
                  <span>Add Option</span>
                </button>
              </div>
              
              <div className="space-y-3">
                {options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="correct-answer"
                      checked={correctAnswer === index}
                      onChange={() => setCorrectAnswer(index)}
                      className="w-4 h-4 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="text-gray-300 font-medium w-6">
                      {String.fromCharCode(65 + index)}.
                    </span>
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => updateOption(index, e.target.value)}
                      placeholder={`Option ${String.fromCharCode(65 + index)}`}
                      className="flex-1 p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    {options.length > 2 && (
                      <button
                        onClick={() => removeOption(index)}
                        className="p-2 text-red-400 hover:text-red-300 transition-colors"
                      >
                        ×
                      </button>
                    )}
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-400 mt-2">
                Select the radio button next to the correct answer
              </p>
            </div>
          )}

          {/* Timer Settings */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Timer Settings
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <span className="text-gray-300">Enable Timer</span>
                <input 
                  type="checkbox" 
                  checked={timerEnabled}
                  onChange={(e) => setTimerEnabled(e.target.checked)}
                  className="w-4 h-4 text-purple-600 rounded" 
                />
              </label>
              
              {timerEnabled && (
                <div>
                  <label className="block text-sm text-gray-300 mb-2">
                    Duration: {timerDuration} seconds
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="300"
                    value={timerDuration}
                    onChange={(e) => setTimerDuration(Number(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>10s</span>
                    <span>5min</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Additional Settings */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Additional Settings
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Category
                </label>
                <select 
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option>General</option>
                  <option>Technology</option>
                  <option>Science</option>
                  <option>Business</option>
                  <option>Education</option>
                  <option>Entertainment</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Difficulty
                </label>
                <select 
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option>Easy</option>
                  <option>Medium</option>
                  <option>Hard</option>
                </select>
              </div>
            </div>
            
            <div className="mt-4 space-y-3">
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" />
                <span className="text-gray-300 text-sm">Allow multiple attempts</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" defaultChecked />
                <span className="text-gray-300 text-sm">Show results after submission</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" />
                <span className="text-gray-300 text-sm">Randomize answer order</span>
              </label>
            </div>
          </div>
        </div>

        {/* Preview and Actions */}
        <div className="space-y-6">
          {/* Preview */}
          {renderPreview()}

          {/* Action Buttons */}
          <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
            <h3 className="text-xl font-semibold text-white mb-4">Actions</h3>
            
            <div className="space-y-3">
              <button className="w-full flex items-center justify-center space-x-2 p-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                <Save className="h-5 w-5" />
                <span>Save as Draft</span>
              </button>
              
              <button className="w-full flex items-center justify-center space-x-2 p-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                <Eye className="h-5 w-5" />
                <span>Preview Poll</span>
              </button>
              
              <button 
                disabled={!pollQuestion.trim()}
                className="w-full flex items-center justify-center space-x-2 p-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-all transform hover:scale-105 font-semibold"
              >
                <Play className="h-5 w-5" />
                <span>Create Poll</span>
              </button>
            </div>
            
            {/* Poll Summary */}
            <div className="mt-6 pt-4 border-t border-gray-600">
              <h4 className="text-white font-medium mb-3">Poll Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Type:</span>
                  <span className="text-white capitalize">{questionType.replace('-', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Category:</span>
                  <span className="text-white">{category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Difficulty:</span>
                  <span className="text-white">{difficulty}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Timer:</span>
                  <span className="text-white">{timerEnabled ? `${timerDuration}s` : 'Disabled'}</span>
                </div>
                {questionType === 'multiple-choice' && (
                  <div className="flex justify-between">
                    <span className="text-gray-300">Options:</span>
                    <span className="text-white">{options.filter(opt => opt.trim()).length}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateManualPoll;
import React, { useState } from 'react';
import { Plus, Copy, RefreshCw, Users, Upload, FileText, Settings, Zap } from 'lucide-react';

const CreatePollSession: React.FC = () => {
  const [roomCode, setRoomCode] = useState('30Z8G0');
  const [roomName, setRoomName] = useState('');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const generateNewCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setRoomCode(result);
  };

  const copyRoomCode = () => {
    navigator.clipboard.writeText(roomCode);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setCsvFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setCsvFile(e.target.files[0]);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-2xl p-8 border border-purple-500/30">
        <h2 className="text-3xl font-bold text-white mb-4 flex items-center">
          <Plus className="h-8 w-8 mr-3 text-green-400" />
          Create Poll Session
        </h2>
        <p className="text-purple-200">Set up a new interactive polling session for your participants</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Room Setup */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Settings className="h-5 w-5 mr-2 text-blue-400" />
            Room Configuration
          </h3>

          <div className="space-y-6">
            {/* Room Name */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Room Name *
              </label>
              <input
                type="text"
                value={roomName}
                onChange={(e) => setRoomName(e.target.value)}
                placeholder="Enter a room name (e.g. Math Quiz, Science Poll)"
                className="w-full p-4 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
              />
            </div>

            {/* Room Code */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Room Code
              </label>
              <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl font-mono font-bold text-white tracking-wider mb-2">
                    {roomCode}
                  </div>
                  <p className="text-purple-300 text-sm">Share this code with participants</p>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={copyRoomCode}
                    className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Copy className="h-4 w-4" />
                    <span>Copy Code</span>
                  </button>
                  <button
                    onClick={generateNewCode}
                    className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Generate New</span>
                  </button>
                </div>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Students will use this code to join your poll session
              </p>
            </div>

            {/* Session Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Participants
                </label>
                <input
                  type="number"
                  defaultValue={50}
                  className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Session Duration
                </label>
                <select className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option>30 minutes</option>
                  <option>1 hour</option>
                  <option>2 hours</option>
                  <option>No limit</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Participant Invitation */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Users className="h-5 w-5 mr-2 text-green-400" />
            Invite Students
          </h3>

          <div className="space-y-6">
            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                OPTIONAL
              </label>
              <div
                className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all ${
                  dragActive
                    ? 'border-purple-400 bg-purple-600/20'
                    : 'border-gray-600 hover:border-purple-400 hover:bg-purple-600/10'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                
                {csvFile ? (
                  <div className="space-y-2">
                    <FileText className="h-12 w-12 text-green-400 mx-auto" />
                    <p className="text-green-400 font-medium">{csvFile.name}</p>
                    <p className="text-gray-400 text-sm">File uploaded successfully</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                    <p className="text-gray-300">
                      <span className="font-medium">Drag & drop your CSV or Excel file</span>
                    </p>
                    <p className="text-gray-400">or click to browse files</p>
                  </div>
                )}
              </div>
              <p className="text-gray-400 text-sm mt-2">
                CSV must contain 'email' column (optional: 'name' column)
              </p>
            </div>

            {/* Manual Invitation */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Or invite manually
              </label>
              <textarea
                placeholder="Enter email addresses separated by commas..."
                rows={4}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              />
            </div>

            {/* Invitation Preview */}
            <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4">
              <h4 className="text-blue-300 font-medium mb-2">Invitation Preview</h4>
              <div className="text-sm text-blue-200 space-y-1">
                <p>Subject: Join our interactive poll session</p>
                <p>Room Code: <span className="font-mono font-bold">{roomCode}</span></p>
                <p>Room: {roomName || 'Untitled Session'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Type Selection */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-xl font-semibold text-white mb-6">Session Type</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-6 hover:bg-purple-600/30 transition-all cursor-pointer group">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-white font-semibold">AI-Powered Session</h4>
                <p className="text-purple-200 text-sm">Auto-generate questions from audio</p>
              </div>
            </div>
            <ul className="text-purple-300 text-sm space-y-1">
              <li>• Real-time speech-to-text conversion</li>
              <li>• Automatic question generation</li>
              <li>• Context-aware poll creation</li>
              <li>• Smart difficulty adjustment</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-blue-600/20 to-cyan-600/20 border border-blue-500/30 rounded-lg p-6 hover:bg-blue-600/30 transition-all cursor-pointer group">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-white font-semibold">Manual Session</h4>
                <p className="text-blue-200 text-sm">Create questions manually</p>
              </div>
            </div>
            <ul className="text-blue-300 text-sm space-y-1">
              <li>• Custom question creation</li>
              <li>• Multiple question types</li>
              <li>• Pre-planned poll sequences</li>
              <li>• Full control over content</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
          Save as Draft
        </button>
        
        <div className="flex space-x-4">
          <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            Preview Session
          </button>
          <button className="px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-lg transition-all transform hover:scale-105 font-semibold">
            Create Poll Session
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreatePollSession;
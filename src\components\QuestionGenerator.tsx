import React, { useState, useEffect } from 'react';
import { Brain, Plus, Edit3, Send } from 'lucide-react';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  category: string;
}

interface QuestionGeneratorProps {
  transcribedText: string;
  onQuestionGenerated: (question: Question) => void;
  onQuestionReview: (question: Question) => void;
}

const QuestionGenerator: React.FC<QuestionGeneratorProps> = ({
  transcribedText,
  onQuestionGenerated,
  onQuestionReview
}) => {
  const [generatedQuestions, setGeneratedQuestions] = useState<Question[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const sampleQuestions: Question[] = [
    {
      id: '1',
      question: 'What is the most important factor for successful team collaboration?',
      options: ['Clear communication', 'Regular meetings', 'Shared tools', 'Strong leadership'],
      correctAnswer: 0,
      category: 'Teamwork'
    },
    {
      id: '2',
      question: 'Which project management methodology emphasizes iterative development?',
      options: ['Waterfall', 'Agile', 'Kanban', 'Lean'],
      correctAnswer: 1,
      category: 'Project Management'
    },
    {
      id: '3',
      question: 'What percentage of communication is non-verbal?',
      options: ['25%', '45%', '55%', '75%'],
      correctAnswer: 2,
      category: 'Communication'
    }
  ];

  useEffect(() => {
    if (transcribedText && transcribedText.length > 50) {
      // Simulate AI question generation based on transcribed text
      const timer = setTimeout(() => {
        generateQuestion();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [transcribedText]);

  const generateQuestion = () => {
    setIsGenerating(true);
    
    // Simulate AI processing time
    setTimeout(() => {
      const randomQuestion = sampleQuestions[Math.floor(Math.random() * sampleQuestions.length)];
      const newQuestion = {
        ...randomQuestion,
        id: Date.now().toString()
      };
      
      setGeneratedQuestions(prev => [...prev, newQuestion]);
      onQuestionGenerated(newQuestion);
      setIsGenerating(false);
    }, 2000);
  };

  const pushQuestionLive = (question: Question) => {
    // Simulate pushing question to live poll
    console.log('Pushing question live:', question);
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-white flex items-center">
          <Brain className="h-5 w-5 mr-2" />
          AI Question Generator
        </h3>
        <button
          onClick={generateQuestion}
          disabled={isGenerating}
          className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white px-3 py-2 rounded-lg transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Generate</span>
        </button>
      </div>

      {isGenerating && (
        <div className="mb-4 p-4 bg-blue-600/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-blue-400">Analyzing content and generating questions...</span>
          </div>
        </div>
      )}

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {generatedQuestions.map((question) => (
          <div key={question.id} className="bg-black/20 rounded-lg p-4 border border-gray-600">
            <div className="flex items-start justify-between mb-3">
              <span className="text-xs bg-purple-600 text-white px-2 py-1 rounded-full">
                {question.category}
              </span>
            </div>
            
            <h4 className="text-white font-medium mb-3">{question.question}</h4>
            
            <div className="space-y-2 mb-4">
              {question.options.map((option, index) => (
                <div 
                  key={index} 
                  className={`p-2 rounded text-sm ${
                    index === question.correctAnswer 
                      ? 'bg-green-600/20 border border-green-500/30 text-green-200' 
                      : 'bg-gray-600/20 border border-gray-500/30 text-gray-300'
                  }`}
                >
                  {String.fromCharCode(65 + index)}. {option}
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => onQuestionReview(question)}
                className="flex items-center space-x-1 bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                <Edit3 className="h-3 w-3" />
                <span>Review</span>
              </button>
              <button
                onClick={() => pushQuestionLive(question)}
                className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                <Send className="h-3 w-3" />
                <span>Push Live</span>
              </button>
            </div>
          </div>
        ))}

        {generatedQuestions.length === 0 && !isGenerating && (
          <div className="text-center py-8">
            <Brain className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-400">
              Start recording to generate questions automatically
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionGenerator;
import React, { useState } from 'react';
import { Download, FileText, FileSpreadsheet } from 'lucide-react';

const ExportResults: React.FC = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'pdf'>('csv');

  const mockData = {
    sessionInfo: {
      title: 'Team Meeting Poll Session',
      date: new Date().toLocaleDateString(),
      duration: '45 minutes',
      totalParticipants: 15,
      totalQuestions: 8
    },
    questions: [
      {
        question: 'What is the most important factor for team collaboration?',
        options: ['Clear communication', 'Regular meetings', 'Shared tools', 'Strong leadership'],
        correctAnswer: 0,
        responses: [8, 3, 2, 2],
        responseTime: 2.3
      },
      {
        question: 'Which project management methodology do you prefer?',
        options: ['Agile', 'Waterfall', 'Kanban', 'Scrum'],
        correctAnswer: 0,
        responses: [9, 2, 2, 2],
        responseTime: 1.8
      }
    ],
    participants: [
      { name: '<PERSON>', score: 95, correctAnswers: 7, avgResponseTime: 1.8 },
      { name: '<PERSON>', score: 88, correctAnswers: 6, avgResponseTime: 1.2 },
      { name: 'Carol Davis', score: 82, correctAnswers: 6, avgResponseTime: 2.1 }
    ]
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    // Simulate export processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (exportFormat === 'csv') {
      exportToCSV();
    } else {
      exportToPDF();
    }
    
    setIsExporting(false);
  };

  const exportToCSV = () => {
    const csvContent = generateCSVContent();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `poll-results-${Date.now()}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const exportToPDF = () => {
    // Simulate PDF generation
    const pdfContent = generatePDFContent();
    console.log('PDF Content:', pdfContent);
    // In a real implementation, you would use a library like jsPDF
    alert('PDF export feature would be implemented with jsPDF library');
  };

  const generateCSVContent = () => {
    let csv = 'Poll Results Export\n\n';
    
    // Session Info
    csv += 'Session Information\n';
    csv += `Title,${mockData.sessionInfo.title}\n`;
    csv += `Date,${mockData.sessionInfo.date}\n`;
    csv += `Duration,${mockData.sessionInfo.duration}\n`;
    csv += `Total Participants,${mockData.sessionInfo.totalParticipants}\n`;
    csv += `Total Questions,${mockData.sessionInfo.totalQuestions}\n\n`;
    
    // Questions and Results
    csv += 'Question Results\n';
    csv += 'Question,Option A,Option B,Option C,Option D,Correct Answer,Avg Response Time\n';
    
    mockData.questions.forEach((q, index) => {
      csv += `"${q.question}"`;
      q.responses.forEach(response => csv += `,${response}`);
      csv += `,${q.options[q.correctAnswer]},${q.responseTime}s\n`;
    });
    
    csv += '\nParticipant Statistics\n';
    csv += 'Name,Score,Correct Answers,Average Response Time\n';
    
    mockData.participants.forEach(p => {
      csv += `${p.name},${p.score}%,${p.correctAnswers},${p.avgResponseTime}s\n`;
    });
    
    return csv;
  };

  const generatePDFContent = () => {
    return {
      title: 'Poll Results Report',
      sessionInfo: mockData.sessionInfo,
      questions: mockData.questions,
      participants: mockData.participants
    };
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
        <Download className="h-5 w-5 mr-2" />
        Export Results
      </h3>

      <div className="space-y-4">
        {/* Format Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Export Format
          </label>
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => setExportFormat('csv')}
              className={`p-3 rounded-lg border transition-all flex items-center justify-center space-x-2 ${
                exportFormat === 'csv'
                  ? 'bg-purple-600 border-purple-500 text-white'
                  : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
              }`}
            >
              <FileSpreadsheet className="h-4 w-4" />
              <span>CSV</span>
            </button>
            <button
              onClick={() => setExportFormat('pdf')}
              className={`p-3 rounded-lg border transition-all flex items-center justify-center space-x-2 ${
                exportFormat === 'pdf'
                  ? 'bg-purple-600 border-purple-500 text-white'
                  : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
              }`}
            >
              <FileText className="h-4 w-4" />
              <span>PDF</span>
            </button>
          </div>
        </div>

        {/* Export Preview */}
        <div className="bg-black/20 rounded-lg p-4 border border-gray-600">
          <h4 className="text-white font-medium mb-3">Export Preview</h4>
          <div className="text-sm space-y-1 text-gray-300">
            <p>• Session: {mockData.sessionInfo.title}</p>
            <p>• Participants: {mockData.sessionInfo.totalParticipants}</p>
            <p>• Questions: {mockData.sessionInfo.totalQuestions}</p>
            <p>• Duration: {mockData.sessionInfo.duration}</p>
            <p>• Date: {mockData.sessionInfo.date}</p>
          </div>
        </div>

        {/* Export Button */}
        <button
          onClick={handleExport}
          disabled={isExporting}
          className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
        >
          {isExporting ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              <span>Exporting...</span>
            </>
          ) : (
            <>
              <Download className="h-5 w-5" />
              <span>Export {exportFormat.toUpperCase()}</span>
            </>
          )}
        </button>

        {/* Recent Exports */}
        <div className="pt-4 border-t border-gray-600">
          <h4 className="text-white font-medium mb-2">Recent Exports</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between text-gray-300">
              <span>team-meeting-results.csv</span>
              <span className="text-xs">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between text-gray-300">
              <span>weekly-poll-summary.pdf</span>
              <span className="text-xs">1 day ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportResults;
import nodemailer from 'nodemailer';
import logger from './logger.js';

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private isConfigured = false;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      // Check if email configuration is available
      const emailConfig = {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      };

      // Only create transporter if credentials are provided
      if (emailConfig.auth.user && emailConfig.auth.pass) {
        this.transporter = nodemailer.createTransporter(emailConfig);
        this.isConfigured = true;
        logger.info('Email service configured successfully');
      } else {
        logger.warn('Email credentials not provided. Email service will use console logging for development.');
        this.isConfigured = false;
      }
    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      this.isConfigured = false;
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        // Development mode - log email to console
        logger.info('📧 Email would be sent (development mode):');
        logger.info(`To: ${options.to}`);
        logger.info(`Subject: ${options.subject}`);
        logger.info(`Content: ${options.text || options.html}`);
        return true;
      }

      // Production mode - send actual email
      const mailOptions = {
        from: `"Live Meeting Poll Platform" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${options.to}. Message ID: ${result.messageId}`);
      return true;
    } catch (error) {
      logger.error('Failed to send email:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string, resetToken: string, userName: string): Promise<boolean> {
    const resetUrl = `${process.env.CLIENT_URL || 'http://localhost:5173'}/reset-password?token=${resetToken}`;
    
    const subject = 'Password Reset Request - Live Meeting Poll Platform';
    
    const text = `
Hello ${userName},

You have requested to reset your password for your Live Meeting Poll Platform account.

Please click on the following link to reset your password:
${resetUrl}

This link will expire in 10 minutes for security reasons.

If you did not request this password reset, please ignore this email and your password will remain unchanged.

Best regards,
Live Meeting Poll Platform Team
    `;

    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Request</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .button:hover { background: #5a6fd8; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔐 Password Reset Request</h1>
        <p>Live Meeting Poll Platform</p>
    </div>
    <div class="content">
        <h2>Hello ${userName},</h2>
        <p>You have requested to reset your password for your Live Meeting Poll Platform account.</p>
        <p>Click the button below to reset your password:</p>
        <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
        </p>
        <p>Or copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 5px;">${resetUrl}</p>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This link will expire in 10 minutes for security reasons.
        </div>
        
        <p>If you did not request this password reset, please ignore this email and your password will remain unchanged.</p>
    </div>
    <div class="footer">
        <p>Best regards,<br>Live Meeting Poll Platform Team</p>
        <p><small>This is an automated email. Please do not reply to this message.</small></p>
    </div>
</body>
</html>
    `;

    return this.sendEmail({
      to: email,
      subject,
      text,
      html
    });
  }

  async sendWelcomeEmail(email: string, userName: string): Promise<boolean> {
    const subject = 'Welcome to Live Meeting Poll Platform! 🎉';
    
    const text = `
Hello ${userName},

Welcome to Live Meeting Poll Platform! 🎉

Your account has been successfully created. You can now:
- Create and participate in live polls
- Join meeting sessions
- Track your poll statistics
- Collaborate with others in real-time

Get started by logging into your account at: ${process.env.CLIENT_URL || 'http://localhost:5173'}

If you have any questions or need assistance, feel free to reach out to our support team.

Best regards,
Live Meeting Poll Platform Team
    `;

    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Live Meeting Poll Platform</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .feature { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #667eea; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 Welcome to Live Meeting Poll Platform!</h1>
        <p>Your account is ready to go</p>
    </div>
    <div class="content">
        <h2>Hello ${userName},</h2>
        <p>Welcome to Live Meeting Poll Platform! Your account has been successfully created.</p>
        
        <h3>What you can do now:</h3>
        <div class="feature">📊 <strong>Create Live Polls:</strong> Design interactive polls for your meetings</div>
        <div class="feature">🤝 <strong>Join Sessions:</strong> Participate in real-time polling sessions</div>
        <div class="feature">📈 <strong>Track Statistics:</strong> Monitor your poll performance and engagement</div>
        <div class="feature">⚡ <strong>Real-time Collaboration:</strong> Work with others seamlessly</div>
        
        <p style="text-align: center;">
            <a href="${process.env.CLIENT_URL || 'http://localhost:5173'}" class="button">Get Started</a>
        </p>
        
        <p>If you have any questions or need assistance, feel free to reach out to our support team.</p>
    </div>
    <div class="footer">
        <p>Best regards,<br>Live Meeting Poll Platform Team</p>
        <p><small>This is an automated email. Please do not reply to this message.</small></p>
    </div>
</body>
</html>
    `;

    return this.sendEmail({
      to: email,
      subject,
      text,
      html
    });
  }

  // Test email configuration
  async testConnection(): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        logger.info('Email service not configured - using development mode');
        return true;
      }

      await this.transporter.verify();
      logger.info('Email service connection verified successfully');
      return true;
    } catch (error) {
      logger.error('Email service connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export default new EmailService();

import React, { useState } from 'react';
import { Video, Users, Zap, Link as LinkIcon } from 'lucide-react';

const MeetingIntegration: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [activeMeeting, setActiveMeeting] = useState<any>(null);

  const mockParticipants = [
    { id: '1', name: '<PERSON>', status: 'speaking' },
    { id: '2', name: '<PERSON>', status: 'muted' },
    { id: '3', name: '<PERSON>', status: 'active' },
    { id: '4', name: '<PERSON>', status: 'away' },
    { id: '5', name: '<PERSON>', status: 'active' }
  ];

  const connectToMeeting = () => {
    setIsConnected(true);
    setActiveMeeting({
      platform: 'Zoom',
      meetingId: '123-456-789',
      participants: mockParticipants,
      duration: '00:15:32'
    });
  };

  const generatePollFromDiscussion = () => {
    // Simulate generating a poll from current meeting discussion
    const sampleQuestions = [
      'What should be our priority for next quarter?',
      'Which approach do you think works best for this project?',
      'How should we allocate the remaining budget?'
    ];
    
    const randomQuestion = sampleQuestions[Math.floor(Math.random() * sampleQuestions.length)];
    alert(`Generated poll: "${randomQuestion}"`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'speaking': return 'bg-green-500';
      case 'active': return 'bg-blue-500';
      case 'muted': return 'bg-yellow-500';
      case 'away': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
        <Video className="h-5 w-5 mr-2" />
        Meeting Integration
      </h3>

      {!isConnected ? (
        <div className="text-center space-y-4">
          <div className="bg-black/20 rounded-lg p-6">
            <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-300 mb-4">
              Connect to your video meeting platform to enable real-time poll generation
            </p>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={connectToMeeting}
                className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                <Video className="h-4 w-4" />
                <span>Zoom</span>
              </button>
              <button
                onClick={connectToMeeting}
                className="p-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                <Video className="h-4 w-4" />
                <span>Meet</span>
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Meeting Info */}
          <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-green-400 font-medium">
                Connected to {activeMeeting.platform}
              </span>
              <span className="text-green-400 text-sm">
                {activeMeeting.duration}
              </span>
            </div>
            <p className="text-gray-300 text-sm">
              Meeting ID: {activeMeeting.meetingId}
            </p>
          </div>

          {/* Participants List */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Active Participants ({activeMeeting.participants.length})
            </h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {activeMeeting.participants.map((participant: any) => (
                <div key={participant.id} className="flex items-center justify-between bg-black/20 rounded-lg p-2">
                  <span className="text-gray-300 text-sm">{participant.name}</span>
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(participant.status)}`}></div>
                </div>
              ))}
            </div>
          </div>

          {/* Generate Poll Button */}
          <button
            onClick={generatePollFromDiscussion}
            className="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all"
          >
            <Zap className="h-4 w-4" />
            <span>Generate Poll from Current Discussion</span>
          </button>

          {/* Disconnect */}
          <button
            onClick={() => {
              setIsConnected(false);
              setActiveMeeting(null);
            }}
            className="w-full p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
          >
            Disconnect
          </button>
        </div>
      )}
    </div>
  );
};

export default MeetingIntegration;
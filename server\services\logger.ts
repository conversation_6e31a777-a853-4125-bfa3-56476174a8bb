import winston from 'winston';
import path from 'path';
import config from '../config/env.js';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// Tell winston that you want to link the colors
winston.addColors(logColors);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.stack ? '\n' + info.stack : ''}`
  )
);

// Define file format (without colors for file logs)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: config.NODE_ENV === 'production' ? 'warn' : 'debug',
    format: logFormat
  }),

  // Error log file
  new winston.transports.File({
    filename: path.join(logsDir, 'error.log'),
    level: 'error',
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),

  // Combined log file
  new winston.transports.File({
    filename: path.join(logsDir, 'combined.log'),
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  })
];

// Add HTTP log file in production
if (config.NODE_ENV === 'production') {
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'http.log'),
      level: 'http',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: config.NODE_ENV === 'production' ? 'warn' : 'debug',
  levels: logLevels,
  format: fileFormat,
  transports,
  exitOnError: false
});

// Enhanced logging methods with context
class Logger {
  private winston: winston.Logger;

  constructor(winstonLogger: winston.Logger) {
    this.winston = winstonLogger;
  }

  // Error logging with context
  error(message: string, error?: Error | any, context?: any): void {
    const logData: any = { message };
    
    if (error) {
      if (error instanceof Error) {
        logData.error = {
          name: error.name,
          message: error.message,
          stack: error.stack
        };
      } else {
        logData.error = error;
      }
    }
    
    if (context) {
      logData.context = context;
    }
    
    this.winston.error(logData);
  }

  // Warning logging
  warn(message: string, context?: any): void {
    this.winston.warn({ message, context });
  }

  // Info logging
  info(message: string, context?: any): void {
    this.winston.info({ message, context });
  }

  // HTTP logging
  http(message: string, context?: any): void {
    this.winston.http({ message, context });
  }

  // Debug logging
  debug(message: string, context?: any): void {
    this.winston.debug({ message, context });
  }

  // Database operation logging
  database(operation: string, collection: string, duration: number, success: boolean, error?: Error): void {
    const logData = {
      message: `Database ${operation} on ${collection}`,
      operation,
      collection,
      duration: `${duration.toFixed(2)}ms`,
      success
    };

    if (error) {
      logData.error = {
        name: error.name,
        message: error.message
      };
    }

    if (success) {
      this.debug(`Database ${operation} on ${collection} completed in ${duration.toFixed(2)}ms`);
    } else {
      this.error(`Database ${operation} on ${collection} failed after ${duration.toFixed(2)}ms`, error);
    }
  }

  // Authentication logging
  auth(action: string, userId?: string, email?: string, ip?: string, success: boolean = true, error?: Error): void {
    const logData = {
      message: `Auth ${action}`,
      action,
      userId,
      email,
      ip,
      success,
      timestamp: new Date().toISOString()
    };

    if (error) {
      logData.error = {
        name: error.name,
        message: error.message
      };
    }

    if (success) {
      this.info(`Auth ${action} successful`, logData);
    } else {
      this.warn(`Auth ${action} failed`, logData);
    }
  }

  // Session logging
  session(action: string, sessionId: string, userId?: string, context?: any): void {
    this.info(`Session ${action}`, {
      action,
      sessionId,
      userId,
      context,
      timestamp: new Date().toISOString()
    });
  }

  // Performance logging
  performance(operation: string, duration: number, context?: any): void {
    const level = duration > 1000 ? 'warn' : 'info';
    const message = `Performance: ${operation} took ${duration.toFixed(2)}ms`;
    
    this.winston.log(level, {
      message,
      operation,
      duration: `${duration.toFixed(2)}ms`,
      context,
      timestamp: new Date().toISOString()
    });
  }

  // Security logging
  security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', context?: any): void {
    const message = `Security Event: ${event}`;
    
    const logData = {
      message,
      event,
      severity,
      context,
      timestamp: new Date().toISOString()
    };

    switch (severity) {
      case 'critical':
      case 'high':
        this.error(message, null, logData);
        break;
      case 'medium':
        this.warn(message, logData);
        break;
      case 'low':
      default:
        this.info(message, logData);
        break;
    }
  }

  // API request logging
  apiRequest(method: string, url: string, statusCode: number, duration: number, userId?: string, ip?: string): void {
    const message = `${method} ${url} ${statusCode} - ${duration.toFixed(2)}ms`;
    
    const logData = {
      method,
      url,
      statusCode,
      duration: `${duration.toFixed(2)}ms`,
      userId,
      ip,
      timestamp: new Date().toISOString()
    };

    if (statusCode >= 500) {
      this.error(message, null, logData);
    } else if (statusCode >= 400) {
      this.warn(message, logData);
    } else {
      this.http(message, logData);
    }
  }

  // Business logic logging
  business(event: string, context?: any): void {
    this.info(`Business Event: ${event}`, {
      event,
      context,
      timestamp: new Date().toISOString()
    });
  }

  // Get winston instance for advanced usage
  getWinstonLogger(): winston.Logger {
    return this.winston;
  }
}

// Create enhanced logger instance
const enhancedLogger = new Logger(logger);

// Export both the enhanced logger and winston instance
export default enhancedLogger;
export { logger as winstonLogger };

// Helper function to create child loggers with context
export const createChildLogger = (context: any) => {
  const childLogger = logger.child(context);
  return new Logger(childLogger);
};

// Utility function to log unhandled errors
export const logUnhandledError = (error: Error, context?: any) => {
  enhancedLogger.error('Unhandled Error', error, {
    context,
    timestamp: new Date().toISOString(),
    processId: process.pid
  });
};

// Utility function to log unhandled promise rejections
export const logUnhandledRejection = (reason: any, promise: Promise<any>) => {
  enhancedLogger.error('Unhandled Promise Rejection', reason, {
    promise: promise.toString(),
    timestamp: new Date().toISOString(),
    processId: process.pid
  });
};

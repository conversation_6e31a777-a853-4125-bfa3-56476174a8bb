import React, { useState } from 'react';
import { 
  <PERSON>, 
  Filter, 
  <PERSON>ting<PERSON>, 
  <PERSON>, 
  Play, 
  Clock, 
  CheckCircle, 
  XCircle,
  RotateCcw,
  Zap,
  Target,
  TrendingUp
} from 'lucide-react';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  confidence: number;
  status: 'pending' | 'approved' | 'rejected';
  category: string;
  tags: string[];
  estimatedTime: number;
}

const AIQuestionsFeed: React.FC = () => {
  const [isAutoLaunchEnabled, setIsAutoLaunchEnabled] = useState(true);
  const [isTimerEnabled, setIsTimerEnabled] = useState(true);
  const [defaultTimer, setDefaultTimer] = useState(30);
  const [minConfidence, setMinConfidence] = useState(80);
  const [difficultyFilter, setDifficultyFilter] = useState('All Levels');
  const [isGenerating, setIsGenerating] = useState(false);

  const [questions, setQuestions] = useState<Question[]>([
    {
      id: '1',
      question: 'What is the primary purpose of React hooks?',
      options: [
        'To replace class components entirely',
        'To add state and lifecycle methods to functional components',
        'To improve performance of React applications',
        'To handle routing in React applications'
      ],
      correctAnswer: 1,
      difficulty: 'Medium',
      confidence: 92,
      status: 'pending',
      category: 'React',
      tags: ['Hooks', 'Functional Components'],
      estimatedTime: 30
    },
    {
      id: '2',
      question: 'Which method is used to update state in a functional component?',
      options: ['this.setState()', 'useState()', 'updateState()', 'setState()'],
      correctAnswer: 1,
      difficulty: 'Easy',
      confidence: 89,
      status: 'pending',
      category: 'React',
      tags: ['State', 'useState'],
      estimatedTime: 25
    },
    {
      id: '3',
      question: 'What is the correct way to handle side effects in React?',
      options: ['componentDidMount', 'useEffect', 'useCallback', 'useMemo'],
      correctAnswer: 1,
      difficulty: 'Medium',
      confidence: 95,
      status: 'approved',
      category: 'React',
      tags: ['Side Effects', 'useEffect'],
      estimatedTime: 35
    }
  ]);

  const handleQuestionAction = (id: string, action: 'approve' | 'reject') => {
    setQuestions(prev => prev.map(q => 
      q.id === id ? { ...q, status: action === 'approve' ? 'approved' : 'rejected' } : q
    ));
  };

  const regenerateQuestions = () => {
    setIsGenerating(true);
    setTimeout(() => {
      setIsGenerating(false);
      // Add new questions logic here
    }, 2000);
  };

  const stopAutoGeneration = () => {
    setIsAutoLaunchEnabled(false);
  };

  const pendingCount = questions.filter(q => q.status === 'pending').length;
  const approvedCount = questions.filter(q => q.status === 'approved').length;
  const avgConfidence = Math.round(questions.reduce((acc, q) => acc + q.confidence, 0) / questions.length);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-2xl font-semibold text-white flex items-center">
            <Brain className="h-6 w-6 mr-3 text-purple-400" />
            Review and manage AI-generated questions
          </h3>
          <div className="flex items-center space-x-2">
            <span className="bg-yellow-600 text-yellow-100 px-3 py-1 rounded-full text-sm font-medium">
              {pendingCount} Pending
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Filter Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Filter className="h-5 w-5 mr-2 text-blue-400" />
            Filter Settings
          </h4>

          <div className="space-y-6">
            {/* Difficulty Level */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select 
                value={difficultyFilter}
                onChange={(e) => setDifficultyFilter(e.target.value)}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option>All Levels</option>
                <option>Easy</option>
                <option>Medium</option>
                <option>Hard</option>
              </select>
            </div>

            {/* Min Confidence */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Min Confidence: {minConfidence}%
              </label>
              <input
                type="range"
                min="50"
                max="100"
                value={minConfidence}
                onChange={(e) => setMinConfidence(Number(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Auto-Launch Settings */}
            <div className="pt-4 border-t border-gray-600">
              <h5 className="text-white font-medium mb-3">Auto-Launch Settings</h5>
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Enable Auto-Launch</span>
                  <input 
                    type="checkbox" 
                    checked={isAutoLaunchEnabled}
                    onChange={(e) => setIsAutoLaunchEnabled(e.target.checked)}
                    className="w-4 h-4 text-purple-600 rounded" 
                  />
                </label>
                <label className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Timer Enabled</span>
                  <input 
                    type="checkbox" 
                    checked={isTimerEnabled}
                    onChange={(e) => setIsTimerEnabled(e.target.checked)}
                    className="w-4 h-4 text-purple-600 rounded" 
                  />
                </label>
                <div>
                  <label className="block text-sm text-gray-300 mb-1">Default Timer: {defaultTimer}s</label>
                  <input
                    type="range"
                    min="10"
                    max="120"
                    value={defaultTimer}
                    onChange={(e) => setDefaultTimer(Number(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="space-y-3">
              <button
                onClick={stopAutoGeneration}
                className="w-full flex items-center justify-center space-x-2 p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <Square className="h-4 w-4" />
                <span>Stop Auto-Generated Questions</span>
              </button>
              <button
                onClick={regenerateQuestions}
                disabled={isGenerating}
                className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                <RotateCcw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
                <span>Regenerate Questions</span>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            Quick Stats
          </h4>

          <div className="space-y-4">
            <div className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-purple-300">Total Questions</span>
                <span className="text-white text-xl font-bold">{questions.length}</span>
              </div>
            </div>

            <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-green-300">Approved</span>
                <span className="text-white text-xl font-bold">{approvedCount}</span>
              </div>
            </div>

            <div className="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-yellow-300">Pending</span>
                <span className="text-white text-xl font-bold">{pendingCount}</span>
              </div>
            </div>

            <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-blue-300">Avg Confidence</span>
                <span className="text-white text-xl font-bold">{avgConfidence}%</span>
              </div>
            </div>
          </div>

          {/* Queue Settings */}
          <div className="mt-6 pt-4 border-t border-gray-600">
            <h5 className="text-white font-medium mb-3">Question Queue</h5>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Questions Per Poll:</span>
                <span className="text-white">5</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* AI Generation Status */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Zap className="h-5 w-5 mr-2 text-yellow-400" />
            AI Generation Status
          </h4>

          <div className="space-y-4">
            <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white font-medium">AI Engine Active</span>
              </div>
              <p className="text-gray-300 text-sm">Analyzing content and generating questions...</p>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Processing Speed:</span>
                <span className="text-green-400">Fast</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Quality Score:</span>
                <span className="text-blue-400">Excellent</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Next Generation:</span>
                <span className="text-purple-400">2 minutes</span>
              </div>
            </div>

            {/* Generation Progress */}
            <div className="pt-4 border-t border-gray-600">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">Generation Progress</span>
                <span className="text-white">75%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Questions List */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h4 className="text-xl font-semibold text-white mb-6">Question Queue</h4>

        <div className="space-y-4">
          {questions.map((question) => (
            <div key={question.id} className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-6 hover:bg-purple-500/20 transition-all">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    question.difficulty === 'Easy' ? 'bg-green-600 text-green-100' :
                    question.difficulty === 'Medium' ? 'bg-yellow-600 text-yellow-100' :
                    'bg-red-600 text-red-100'
                  }`}>
                    {question.difficulty}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    question.status === 'pending' ? 'bg-yellow-600 text-yellow-100' :
                    question.status === 'approved' ? 'bg-green-600 text-green-100' :
                    'bg-red-600 text-red-100'
                  }`}>
                    {question.status}
                  </span>
                  <span className="text-blue-400 text-sm font-medium">{question.confidence}% confidence</span>
                  <div className="flex items-center space-x-1 text-gray-400 text-sm">
                    <Clock className="h-3 w-3" />
                    <span>{question.estimatedTime}s</span>
                  </div>
                </div>
              </div>

              <h5 className="text-white font-medium mb-3 text-lg">{question.question}</h5>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
                {question.options.map((option, index) => (
                  <div 
                    key={index} 
                    className={`p-3 rounded text-sm border ${
                      index === question.correctAnswer 
                        ? 'bg-green-600/20 border-green-500/30 text-green-200' 
                        : 'bg-gray-600/20 border-gray-500/30 text-gray-300'
                    }`}
                  >
                    {String.fromCharCode(65 + index)}. {option}
                  </div>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-xs bg-purple-600 text-white px-2 py-1 rounded">
                    {question.category}
                  </span>
                  {question.tags.map((tag, index) => (
                    <span key={index} className="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded">
                      {tag}
                    </span>
                  ))}
                </div>

                {question.status === 'pending' && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleQuestionAction(question.id, 'reject')}
                      className="flex items-center space-x-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      <XCircle className="h-3 w-3" />
                      <span>Reject</span>
                    </button>
                    <button
                      onClick={() => handleQuestionAction(question.id, 'approve')}
                      className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      <CheckCircle className="h-3 w-3" />
                      <span>Approve</span>
                    </button>
                    <button className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      <Play className="h-3 w-3" />
                      <span>Launch</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AIQuestionsFeed;
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import config from './env.js';

// Swagger definition
const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Live Meeting Poll Platform API',
    version: '1.0.0',
    description: 'A comprehensive API for managing live meeting polls, sessions, and real-time interactions',
    contact: {
      name: 'API Support',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: `http://localhost:${config.PORT}`,
      description: 'Development server'
    },
    {
      url: config.CLIENT_URL || 'http://localhost:3000',
      description: 'Production server'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT Authorization header using the Bearer scheme'
      }
    },
    schemas: {
      User: {
        type: 'object',
        required: ['name', 'email', 'role'],
        properties: {
          _id: {
            type: 'string',
            description: 'User ID'
          },
          name: {
            type: 'string',
            description: 'User full name',
            example: 'John Doe'
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'User email address',
            example: '<EMAIL>'
          },
          role: {
            type: 'string',
            enum: ['general', 'participant', 'host', 'admin'],
            description: 'User role',
            example: 'participant'
          },
          avatar: {
            type: 'string',
            description: 'User avatar URL',
            example: '/uploads/avatars/user123.jpg'
          },
          isActive: {
            type: 'boolean',
            description: 'User active status',
            example: true
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'User creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'User last update timestamp'
          }
        }
      },
      Session: {
        type: 'object',
        required: ['title', 'creator'],
        properties: {
          _id: {
            type: 'string',
            description: 'Session ID'
          },
          title: {
            type: 'string',
            description: 'Session title',
            example: 'Weekly Team Meeting'
          },
          description: {
            type: 'string',
            description: 'Session description',
            example: 'Discussion about project progress and upcoming milestones'
          },
          creator: {
            $ref: '#/components/schemas/User'
          },
          participants: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/User'
            },
            description: 'List of session participants'
          },
          isActive: {
            type: 'boolean',
            description: 'Session active status',
            example: true
          },
          isPublic: {
            type: 'boolean',
            description: 'Session visibility',
            example: true
          },
          maxParticipants: {
            type: 'integer',
            description: 'Maximum number of participants',
            example: 50
          },
          startTime: {
            type: 'string',
            format: 'date-time',
            description: 'Session start time'
          },
          endTime: {
            type: 'string',
            format: 'date-time',
            description: 'Session end time'
          },
          settings: {
            type: 'object',
            properties: {
              allowAnonymous: {
                type: 'boolean',
                example: false
              },
              requireApproval: {
                type: 'boolean',
                example: true
              },
              enableChat: {
                type: 'boolean',
                example: true
              },
              enablePolls: {
                type: 'boolean',
                example: true
              }
            }
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Session creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Session last update timestamp'
          }
        }
      },
      Poll: {
        type: 'object',
        required: ['question', 'options', 'session', 'creator'],
        properties: {
          _id: {
            type: 'string',
            description: 'Poll ID'
          },
          question: {
            type: 'string',
            description: 'Poll question',
            example: 'What should be our next project priority?'
          },
          options: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  example: 'Feature Development'
                },
                votes: {
                  type: 'integer',
                  example: 5
                }
              }
            },
            description: 'Poll options with vote counts'
          },
          session: {
            type: 'string',
            description: 'Session ID this poll belongs to'
          },
          creator: {
            $ref: '#/components/schemas/User'
          },
          type: {
            type: 'string',
            enum: ['multiple-choice', 'single-choice', 'text', 'rating'],
            description: 'Poll type',
            example: 'single-choice'
          },
          isActive: {
            type: 'boolean',
            description: 'Poll active status',
            example: true
          },
          allowMultipleVotes: {
            type: 'boolean',
            description: 'Allow multiple votes per user',
            example: false
          },
          isAnonymous: {
            type: 'boolean',
            description: 'Anonymous voting',
            example: true
          },
          endTime: {
            type: 'string',
            format: 'date-time',
            description: 'Poll end time'
          },
          votes: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                user: {
                  type: 'string',
                  description: 'User ID'
                },
                option: {
                  type: 'string',
                  description: 'Selected option'
                },
                timestamp: {
                  type: 'string',
                  format: 'date-time'
                }
              }
            },
            description: 'Poll votes'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Poll creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Poll last update timestamp'
          }
        }
      },
      Error: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                example: 'Resource not found'
              },
              code: {
                type: 'string',
                example: 'RESOURCE_NOT_FOUND'
              }
            }
          },
          requestId: {
            type: 'string',
            description: 'Request ID for tracking'
          }
        }
      },
      Success: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully'
          },
          data: {
            type: 'object',
            description: 'Response data'
          }
        }
      }
    },
    responses: {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                message: 'Authentication required',
                code: 'UNAUTHORIZED'
              }
            }
          }
        }
      },
      ForbiddenError: {
        description: 'Insufficient permissions',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                message: 'Insufficient permissions',
                code: 'FORBIDDEN'
              }
            }
          }
        }
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                message: 'Resource not found',
                code: 'RESOURCE_NOT_FOUND'
              }
            }
          }
        }
      },
      ValidationError: {
        description: 'Validation error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                message: 'Validation failed',
                code: 'VALIDATION_ERROR'
              }
            }
          }
        }
      },
      ServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                message: 'Internal server error',
                code: 'SERVER_ERROR'
              }
            }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

// Options for swagger-jsdoc
const swaggerOptions = {
  definition: swaggerDefinition,
  apis: [
    './server/routes/*.ts',
    './server/controllers/*.ts',
    './server/models/*.ts'
  ]
};

// Generate swagger specification
const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Setup Swagger UI
export const setupSwagger = (app: Express): void => {
  // Swagger UI options
  const swaggerUiOptions = {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Live Meeting Poll Platform API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true
    }
  };

  // Serve swagger docs
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));
  
  // Serve swagger spec as JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
};

export default swaggerSpec;

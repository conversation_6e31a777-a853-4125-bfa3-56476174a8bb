import mongoose, { Document, Schema } from 'mongoose';

export interface IPollOption {
  text: string;
  votes: number;
  voters: mongoose.Types.ObjectId[];
}

export interface IPoll extends Document {
  title: string;
  question: string;
  options: IPollOption[];
  correctAnswer?: number;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'opinion-poll';
  category: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  tags: string[];
  creator: mongoose.Types.ObjectId;
  session: mongoose.Types.ObjectId;
  isActive: boolean;
  isPublished: boolean;
  timeLimit?: number;
  startTime?: Date;
  endTime?: Date;
  totalVotes: number;
  aiGenerated: boolean;
  aiConfidence?: number;
  responses: Array<{
    user: mongoose.Types.ObjectId;
    selectedOption: number;
    responseTime: number;
    isCorrect: boolean;
    submittedAt: Date;
  }>;
  analytics: {
    averageResponseTime: number;
    accuracyRate: number;
    participationRate: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const pollSchema = new Schema<IPoll>({
  title: {
    type: String,
    required: [true, 'Poll title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  question: {
    type: String,
    required: [true, 'Poll question is required'],
    trim: true,
    maxlength: [1000, 'Question cannot exceed 1000 characters']
  },
  options: [{
    text: {
      type: String,
      required: true,
      trim: true,
      maxlength: [500, 'Option text cannot exceed 500 characters']
    },
    votes: {
      type: Number,
      default: 0
    },
    voters: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }]
  }],
  correctAnswer: {
    type: Number,
    default: null
  },
  type: {
    type: String,
    enum: ['multiple-choice', 'true-false', 'short-answer', 'opinion-poll'],
    required: true
  },
  category: {
    type: String,
    required: true,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['Easy', 'Medium', 'Hard'],
    default: 'Medium'
  },
  tags: [{
    type: String,
    trim: true
  }],
  creator: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  session: {
    type: Schema.Types.ObjectId,
    ref: 'Session',
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  timeLimit: {
    type: Number,
    default: 30 // seconds
  },
  startTime: {
    type: Date,
    default: null
  },
  endTime: {
    type: Date,
    default: null
  },
  totalVotes: {
    type: Number,
    default: 0
  },
  aiGenerated: {
    type: Boolean,
    default: false
  },
  aiConfidence: {
    type: Number,
    min: 0,
    max: 100
  },
  responses: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    selectedOption: {
      type: Number,
      required: true
    },
    responseTime: {
      type: Number,
      required: true // in milliseconds
    },
    isCorrect: {
      type: Boolean,
      default: false
    },
    submittedAt: {
      type: Date,
      default: Date.now
    }
  }],
  analytics: {
    averageResponseTime: {
      type: Number,
      default: 0
    },
    accuracyRate: {
      type: Number,
      default: 0
    },
    participationRate: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Indexes for better query performance
pollSchema.index({ creator: 1, createdAt: -1 });
pollSchema.index({ session: 1, isActive: 1 });
pollSchema.index({ category: 1, difficulty: 1 });
pollSchema.index({ isPublished: 1, isActive: 1 });

export default mongoose.model<IPoll>('Poll', pollSchema);
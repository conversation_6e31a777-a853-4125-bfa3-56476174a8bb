import React, { useState } from 'react';
import { X, Save, Trash2 } from 'lucide-react';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  category: string;
}

interface QuestionReviewModalProps {
  question: Question;
  onClose: () => void;
  onSave: (question: Question) => void;
}

const QuestionReviewModal: React.FC<QuestionReviewModalProps> = ({
  question,
  onClose,
  onSave
}) => {
  const [editedQuestion, setEditedQuestion] = useState<Question>(question);

  const handleSave = () => {
    onSave(editedQuestion);
  };

  const updateOption = (index: number, value: string) => {
    const newOptions = [...editedQuestion.options];
    newOptions[index] = value;
    setEditedQuestion({ ...editedQuestion, options: newOptions });
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-white">Review & Edit Question</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Category
            </label>
            <input
              type="text"
              value={editedQuestion.category}
              onChange={(e) => setEditedQuestion({ ...editedQuestion, category: e.target.value })}
              className="w-full p-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Question */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Question
            </label>
            <textarea
              value={editedQuestion.question}
              onChange={(e) => setEditedQuestion({ ...editedQuestion, question: e.target.value })}
              rows={3}
              className="w-full p-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
            />
          </div>

          {/* Options */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Answer Options
            </label>
            <div className="space-y-3">
              {editedQuestion.options.map((option, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="correctAnswer"
                      checked={editedQuestion.correctAnswer === index}
                      onChange={() => setEditedQuestion({ ...editedQuestion, correctAnswer: index })}
                      className="w-4 h-4 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="text-gray-300 font-medium">
                      {String.fromCharCode(65 + index)}.
                    </span>
                  </div>
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updateOption(index, e.target.value)}
                    className="flex-1 p-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder={`Option ${index + 1}`}
                  />
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-400 mt-2">
              Select the radio button next to the correct answer
            </p>
          </div>

          {/* Preview */}
          <div className="bg-black/20 rounded-lg p-4 border border-gray-600">
            <h4 className="text-white font-medium mb-3">Preview</h4>
            <div className="mb-3">
              <span className="text-xs bg-purple-600 text-white px-2 py-1 rounded-full">
                {editedQuestion.category}
              </span>
            </div>
            <p className="text-gray-200 mb-3">{editedQuestion.question}</p>
            <div className="space-y-2">
              {editedQuestion.options.map((option, index) => (
                <div
                  key={index}
                  className={`p-2 rounded text-sm ${
                    index === editedQuestion.correctAnswer
                      ? 'bg-green-600/20 border border-green-500/30 text-green-200'
                      : 'bg-gray-600/20 border border-gray-500/30 text-gray-300'
                  }`}
                >
                  {String.fromCharCode(65 + index)}. {option}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4">
            <button
              onClick={onClose}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              <Trash2 className="h-4 w-4" />
              <span>Discard</span>
            </button>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Save className="h-4 w-4" />
                <span>Save & Push Live</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionReviewModal;
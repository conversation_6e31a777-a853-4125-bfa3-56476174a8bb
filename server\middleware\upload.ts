import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Ensure upload directories exist
const uploadDirs = ['uploads/avatars', 'uploads/sessions', 'uploads/polls'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';
    
    // Determine upload path based on field name
    switch (file.fieldname) {
      case 'avatar':
        uploadPath += 'avatars/';
        break;
      case 'sessionMaterial':
        uploadPath += 'sessions/';
        break;
      case 'pollAttachment':
        uploadPath += 'polls/';
        break;
      default:
        uploadPath += 'misc/';
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  }
});

// File filter
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Define allowed file types for different upload types
  const allowedTypes: { [key: string]: string[] } = {
    avatar: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    sessionMaterial: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ],
    pollAttachment: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain'
    ]
  };

  const fieldAllowedTypes = allowedTypes[file.fieldname] || [];
  
  if (fieldAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Invalid file type for ${file.fieldname}. Allowed types: ${fieldAllowedTypes.join(', ')}`));
  }
};

// File size limits (in bytes)
const fileSizeLimits: { [key: string]: number } = {
  avatar: 5 * 1024 * 1024, // 5MB
  sessionMaterial: 50 * 1024 * 1024, // 50MB
  pollAttachment: 10 * 1024 * 1024 // 10MB
};

// Create multer instances for different upload types
export const uploadAvatar = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: fileSizeLimits.avatar,
    files: 1
  }
}).single('avatar');

export const uploadSessionMaterial = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: fileSizeLimits.sessionMaterial,
    files: 5 // Allow up to 5 files
  }
}).array('sessionMaterial', 5);

export const uploadPollAttachment = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: fileSizeLimits.pollAttachment,
    files: 3 // Allow up to 3 files
  }
}).array('pollAttachment', 3);

// Generic upload middleware
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB max
    files: 10
  }
});

// Error handling middleware for multer
export const handleUploadError = (err: any, req: any, res: any, next: any) => {
  if (err instanceof multer.MulterError) {
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: 'File too large',
          maxSize: '50MB'
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Too many files uploaded'
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Unexpected file field'
        });
      default:
        return res.status(400).json({
          success: false,
          message: 'File upload error',
          error: err.message
        });
    }
  }
  
  if (err.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: err.message
    });
  }
  
  next(err);
};

// Utility function to delete uploaded files
export const deleteFile = (filePath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// Utility function to get file URL
export const getFileUrl = (filePath: string): string => {
  return `/uploads/${filePath.replace('uploads/', '')}`;
};

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'general' | 'participant' | 'host' | 'admin';
  roleHistory: RoleChange[];
  permissions: string[];
  lastRoleSwitch?: Date;
  isVerified: boolean;
}

interface RoleChange {
  from: string;
  to: string;
  date: Date;
  approvedBy?: string;
  reason?: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  switchRole: (newRole: 'participant' | 'host') => Promise<{ success: boolean; requiresApproval?: boolean; message?: string }>;
  requestHostRole: (reason?: string) => Promise<{ success: boolean; message: string }>;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  canSwitchRole: (targetRole: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  // Role permissions mapping
  const rolePermissions = {
    general: ['view_polls', 'participate_polls'],
    participant: ['view_polls', 'participate_polls', 'view_leaderboard', 'view_stats'],
    host: ['view_polls', 'participate_polls', 'create_polls', 'manage_sessions', 'view_analytics', 'export_data', 'manage_participants'],
    admin: ['*'] // All permissions
  };

  const login = async (email: string, password: string) => {
    // Simulate API call with enhanced user data
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser: User = {
      id: '1',
      email,
      name: email.split('@')[0],
      role: 'general', // Start with general role
      roleHistory: [],
      permissions: rolePermissions.general,
      isVerified: true,
      lastRoleSwitch: undefined
    };
    
    setUser(mockUser);
  };

  const register = async (name: string, email: string, password: string) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser: User = {
      id: Date.now().toString(),
      email,
      name,
      role: 'general', // All users start as general
      roleHistory: [],
      permissions: rolePermissions.general,
      isVerified: true,
      lastRoleSwitch: undefined
    };
    
    setUser(mockUser);
  };

  const canSwitchRole = (targetRole: string): boolean => {
    if (!user) return false;
    
    // Check if user switched roles recently (rate limiting)
    if (user.lastRoleSwitch) {
      const timeSinceLastSwitch = Date.now() - user.lastRoleSwitch.getTime();
      const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      
      if (timeSinceLastSwitch < oneDay) {
        return false; // Can only switch once per day
      }
    }
    
    // Define allowed role transitions
    const allowedTransitions: { [key: string]: string[] } = {
      general: ['participant', 'host'],
      participant: ['host'],
      host: ['participant'],
      admin: ['participant', 'host']
    };
    
    return allowedTransitions[user.role]?.includes(targetRole) || false;
  };

  const switchRole = async (newRole: 'participant' | 'host') => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!canSwitchRole(newRole)) {
      return { 
        success: false, 
        message: 'Role switch not allowed. You can only switch roles once per day.' 
      };
    }

    // For host role, require additional verification
    if (newRole === 'host' && user.role !== 'host') {
      return await requestHostRole('User requested host privileges');
    }

    // Immediate role switch for participant or downgrade
    const roleChange: RoleChange = {
      from: user.role,
      to: newRole,
      date: new Date(),
      reason: 'User initiated role switch'
    };

    const updatedUser: User = {
      ...user,
      role: newRole,
      permissions: rolePermissions[newRole],
      roleHistory: [...user.roleHistory, roleChange],
      lastRoleSwitch: new Date()
    };

    setUser(updatedUser);

    // In real implementation, this would make an API call to update the backend
    // and generate a new JWT token with updated permissions
    
    return { 
      success: true, 
      message: `Successfully switched to ${newRole} role` 
    };
  };

  const requestHostRole = async (reason?: string) => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    // Simulate host role approval process
    // In real implementation, this would:
    // 1. Send request to admin for approval
    // 2. Send verification email
    // 3. Log the request for audit purposes
    
    await new Promise(resolve => setTimeout(resolve, 2000));

    // For demo purposes, auto-approve the request
    const roleChange: RoleChange = {
      from: user.role,
      to: 'host',
      date: new Date(),
      reason: reason || 'Host role requested',
      approvedBy: 'system' // In real app, this would be admin ID
    };

    const updatedUser: User = {
      ...user,
      role: 'host',
      permissions: rolePermissions.host,
      roleHistory: [...user.roleHistory, roleChange],
      lastRoleSwitch: new Date()
    };

    setUser(updatedUser);

    return { 
      success: true, 
      message: 'Host role approved! You now have host privileges.' 
    };
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin has all permissions
    if (user.permissions.includes('*')) return true;
    
    return user.permissions.includes(permission);
  };

  const logout = () => {
    setUser(null);
    // In real implementation, invalidate JWT token and clear session
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{ 
      user, 
      login, 
      register, 
      logout, 
      switchRole,
      requestHostRole,
      isAuthenticated,
      hasPermission,
      canSwitchRole
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
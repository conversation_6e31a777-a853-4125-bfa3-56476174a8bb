import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'general' | 'participant' | 'host' | 'admin';
  roleHistory: RoleChange[];
  permissions: string[];
  lastRoleSwitch?: Date;
  isVerified: boolean;
}

interface RoleChange {
  from: string;
  to: string;
  date: Date;
  approvedBy?: string;
  reason?: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  switchRole: (newRole: 'participant' | 'host') => Promise<{ success: boolean; requiresApproval?: boolean; message?: string }>;
  requestHostRole: (reason?: string) => Promise<{ success: boolean; message: string }>;
  setUser: (user: User | null) => void;
  setToken: (token: string) => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  canSwitchRole: (targetRole: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for stored authentication on app load
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const response = await fetch('http://localhost:5000/api/auth/profile', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();
            const frontendUser: User = {
              id: data.user._id,
              email: data.user.email,
              name: data.user.name,
              role: data.user.role,
              roleHistory: data.user.roleHistory || [],
              permissions: rolePermissions[data.user.role as keyof typeof rolePermissions] || rolePermissions.general,
              isVerified: data.user.isVerified,
              lastRoleSwitch: data.user.lastRoleSwitch ? new Date(data.user.lastRoleSwitch) : undefined
            };
            setUser(frontendUser);
          } else {
            // Token is invalid, remove it
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
          }
        } catch (error) {
          console.error('Auth check error:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, []);

  // Role permissions mapping
  const rolePermissions = {
    general: ['view_polls', 'participate_polls'],
    participant: ['view_polls', 'participate_polls', 'view_leaderboard', 'view_stats'],
    host: ['view_polls', 'participate_polls', 'create_polls', 'manage_sessions', 'view_analytics', 'export_data', 'manage_participants'],
    admin: ['*'] // All permissions
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Show detailed validation errors if available
        if (data.errors && Array.isArray(data.errors)) {
          const errorMessages = data.errors.map((err: any) => `${err.field}: ${err.message}`).join('\n');
          throw new Error(`Validation failed:\n${errorMessages}`);
        }
        throw new Error(data.message || 'Login failed');
      }

      // Store tokens
      localStorage.setItem('token', data.token);
      localStorage.setItem('refreshToken', data.refreshToken);

      // Map backend user to frontend user format
      const frontendUser: User = {
        id: data.user._id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role,
        roleHistory: data.user.roleHistory || [],
        permissions: rolePermissions[data.user.role as keyof typeof rolePermissions] || rolePermissions.general,
        isVerified: data.user.isVerified,
        lastRoleSwitch: data.user.lastRoleSwitch ? new Date(data.user.lastRoleSwitch) : undefined
      };

      setUser(frontendUser);
      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (name: string, email: string, password: string) => {
    try {
      const response = await fetch('http://localhost:5000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Show detailed validation errors if available
        if (data.errors && Array.isArray(data.errors)) {
          const errorMessages = data.errors.map((err: any) => `${err.field}: ${err.message}`).join('\n');
          throw new Error(`Validation failed:\n${errorMessages}`);
        }
        throw new Error(data.message || 'Registration failed');
      }

      // Store tokens
      localStorage.setItem('token', data.token);
      localStorage.setItem('refreshToken', data.refreshToken);

      // Map backend user to frontend user format
      const frontendUser: User = {
        id: data.user._id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role,
        roleHistory: data.user.roleHistory || [],
        permissions: rolePermissions[data.user.role as keyof typeof rolePermissions] || rolePermissions.general,
        isVerified: data.user.isVerified,
        lastRoleSwitch: data.user.lastRoleSwitch ? new Date(data.user.lastRoleSwitch) : undefined
      };

      setUser(frontendUser);
      return data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const canSwitchRole = (targetRole: string): boolean => {
    if (!user) return false;
    
    // Check if user switched roles recently (rate limiting)
    if (user.lastRoleSwitch) {
      const timeSinceLastSwitch = Date.now() - user.lastRoleSwitch.getTime();
      const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      
      if (timeSinceLastSwitch < oneDay) {
        return false; // Can only switch once per day
      }
    }
    
    // Define allowed role transitions
    const allowedTransitions: { [key: string]: string[] } = {
      general: ['participant', 'host'],
      participant: ['host'],
      host: ['participant'],
      admin: ['participant', 'host']
    };
    
    return allowedTransitions[user.role]?.includes(targetRole) || false;
  };

  const switchRole = async (newRole: 'participant' | 'host') => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!canSwitchRole(newRole)) {
      return { 
        success: false, 
        message: 'Role switch not allowed. You can only switch roles once per day.' 
      };
    }

    // For host role, require additional verification
    if (newRole === 'host' && user.role !== 'host') {
      return await requestHostRole('User requested host privileges');
    }

    // Immediate role switch for participant or downgrade
    const roleChange: RoleChange = {
      from: user.role,
      to: newRole,
      date: new Date(),
      reason: 'User initiated role switch'
    };

    const updatedUser: User = {
      ...user,
      role: newRole,
      permissions: rolePermissions[newRole],
      roleHistory: [...user.roleHistory, roleChange],
      lastRoleSwitch: new Date()
    };

    setUser(updatedUser);

    // In real implementation, this would make an API call to update the backend
    // and generate a new JWT token with updated permissions
    
    return { 
      success: true, 
      message: `Successfully switched to ${newRole} role` 
    };
  };

  const requestHostRole = async (reason?: string) => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    // Simulate host role approval process
    // In real implementation, this would:
    // 1. Send request to admin for approval
    // 2. Send verification email
    // 3. Log the request for audit purposes
    
    await new Promise(resolve => setTimeout(resolve, 2000));

    // For demo purposes, auto-approve the request
    const roleChange: RoleChange = {
      from: user.role,
      to: 'host',
      date: new Date(),
      reason: reason || 'Host role requested',
      approvedBy: 'system' // In real app, this would be admin ID
    };

    const updatedUser: User = {
      ...user,
      role: 'host',
      permissions: rolePermissions.host,
      roleHistory: [...user.roleHistory, roleChange],
      lastRoleSwitch: new Date()
    };

    setUser(updatedUser);

    return { 
      success: true, 
      message: 'Host role approved! You now have host privileges.' 
    };
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin has all permissions
    if (user.permissions.includes('*')) return true;
    
    return user.permissions.includes(permission);
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        await fetch('http://localhost:5000/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens and user state regardless of API call success
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      setUser(null);
    }
  };

  const isAuthenticated = !!user;

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  // Helper methods for external use (e.g., password reset)
  const setUserExternal = (newUser: User | null) => {
    setUser(newUser);
  };

  const setTokenExternal = (token: string) => {
    localStorage.setItem('token', token);
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      register,
      logout,
      switchRole,
      requestHostRole,
      setUser: setUserExternal,
      setToken: setTokenExternal,
      isAuthenticated,
      hasPermission,
      canSwitchRole
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
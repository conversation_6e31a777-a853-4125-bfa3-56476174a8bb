import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { User<PERSON>he<PERSON>, Shield, Users, Crown, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

const RoleSwitcher: React.FC = () => {
  const { user, switchRole, requestHostRole, canSwitchRole } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [showHostRequest, setShowHostRequest] = useState(false);
  const [hostReason, setHostReason] = useState('');

  if (!user) return null;

  const handleRoleSwitch = async (newRole: 'participant' | 'host') => {
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await switchRole(newRole);
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message || 'Role switched successfully!' });
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to switch role' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred while switching roles' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleHostRequest = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await requestHostRole(hostReason);
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        setShowHostRequest(false);
        setHostReason('');
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred while requesting host role' });
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'general': return <UserCheck className="h-4 w-4" />;
      case 'participant': return <Users className="h-4 w-4" />;
      case 'host': return <Crown className="h-4 w-4" />;
      case 'admin': return <Shield className="h-4 w-4" />;
      default: return <UserCheck className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'general': return 'bg-gray-600';
      case 'participant': return 'bg-blue-600';
      case 'host': return 'bg-purple-600';
      case 'admin': return 'bg-red-600';
      default: return 'bg-gray-600';
    }
  };

  const canSwitchToParticipant = canSwitchRole('participant');
  const canSwitchToHost = canSwitchRole('host');

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
        <UserCheck className="h-5 w-5 mr-2 text-blue-400" />
        Role Management
      </h3>

      {/* Current Role Display */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 p-4 bg-black/20 rounded-lg border border-gray-600">
          <div className={`w-10 h-10 ${getRoleColor(user.role)} rounded-full flex items-center justify-center`}>
            {getRoleIcon(user.role)}
          </div>
          <div>
            <p className="text-white font-medium">Current Role</p>
            <p className="text-gray-300 capitalize">{user.role}</p>
          </div>
          <div className="ml-auto">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)} text-white`}>
              Active
            </span>
          </div>
        </div>
      </div>

      {/* Role Switch Options */}
      <div className="space-y-4 mb-6">
        <h4 className="text-white font-medium">Switch to:</h4>
        
        {/* Participant Role */}
        {user.role !== 'participant' && (
          <button
            onClick={() => handleRoleSwitch('participant')}
            disabled={!canSwitchToParticipant || isLoading}
            className="w-full flex items-center justify-between p-4 bg-blue-600/20 border border-blue-500/30 rounded-lg hover:bg-blue-600/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
          >
            <div className="flex items-center space-x-3">
              <Users className="h-5 w-5 text-blue-400" />
              <div className="text-left">
                <p className="text-white font-medium">Participant</p>
                <p className="text-gray-300 text-sm">Join and participate in polls</p>
              </div>
            </div>
            {canSwitchToParticipant ? (
              <CheckCircle className="h-5 w-5 text-green-400" />
            ) : (
              <Clock className="h-5 w-5 text-yellow-400" />
            )}
          </button>
        )}

        {/* Host Role */}
        {user.role !== 'host' && (
          <div>
            <button
              onClick={() => user.role === 'general' ? setShowHostRequest(true) : handleRoleSwitch('host')}
              disabled={!canSwitchToHost || isLoading}
              className="w-full flex items-center justify-between p-4 bg-purple-600/20 border border-purple-500/30 rounded-lg hover:bg-purple-600/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            >
              <div className="flex items-center space-x-3">
                <Crown className="h-5 w-5 text-purple-400" />
                <div className="text-left">
                  <p className="text-white font-medium">Host</p>
                  <p className="text-gray-300 text-sm">Create and manage polls</p>
                </div>
              </div>
              {user.role === 'general' ? (
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
              ) : canSwitchToHost ? (
                <CheckCircle className="h-5 w-5 text-green-400" />
              ) : (
                <Clock className="h-5 w-5 text-yellow-400" />
              )}
            </button>
            
            {user.role === 'general' && (
              <p className="text-xs text-yellow-400 mt-2 flex items-center">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Host role requires verification
              </p>
            )}
          </div>
        )}
      </div>

      {/* Host Role Request Modal */}
      {showHostRequest && (
        <div className="mb-6 p-4 bg-purple-600/20 border border-purple-500/30 rounded-lg">
          <h4 className="text-white font-medium mb-3">Request Host Role</h4>
          <textarea
            value={hostReason}
            onChange={(e) => setHostReason(e.target.value)}
            placeholder="Please explain why you need host privileges..."
            className="w-full p-3 bg-black/40 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
            rows={3}
          />
          <div className="flex space-x-3 mt-3">
            <button
              onClick={handleHostRequest}
              disabled={isLoading || !hostReason.trim()}
              className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isLoading ? 'Requesting...' : 'Submit Request'}
            </button>
            <button
              onClick={() => setShowHostRequest(false)}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Rate Limiting Info */}
      {user.lastRoleSwitch && (
        <div className="mb-4 p-3 bg-yellow-600/20 border border-yellow-500/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-yellow-400" />
            <p className="text-yellow-300 text-sm">
              Last role switch: {user.lastRoleSwitch.toLocaleDateString()}
            </p>
          </div>
          <p className="text-yellow-400 text-xs mt-1">
            You can switch roles once per day for security reasons
          </p>
        </div>
      )}

      {/* Role History */}
      {user.roleHistory.length > 0 && (
        <div className="mb-4">
          <h4 className="text-white font-medium mb-2">Recent Role Changes</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {user.roleHistory.slice(-3).reverse().map((change, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-black/20 rounded text-sm">
                <span className="text-gray-300">
                  {change.from} → {change.to}
                </span>
                <span className="text-gray-400 text-xs">
                  {change.date.toLocaleDateString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Status Messages */}
      {message && (
        <div className={`p-3 rounded-lg border ${
          message.type === 'success' ? 'bg-green-600/20 border-green-500/30 text-green-300' :
          message.type === 'error' ? 'bg-red-600/20 border-red-500/30 text-red-300' :
          'bg-blue-600/20 border-blue-500/30 text-blue-300'
        }`}>
          <div className="flex items-center space-x-2">
            {message.type === 'success' && <CheckCircle className="h-4 w-4" />}
            {message.type === 'error' && <AlertTriangle className="h-4 w-4" />}
            {message.type === 'info' && <Clock className="h-4 w-4" />}
            <p className="text-sm">{message.text}</p>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <div className="w-6 h-6 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-2 text-gray-300">Processing...</span>
        </div>
      )}
    </div>
  );
};

export default RoleSwitcher;
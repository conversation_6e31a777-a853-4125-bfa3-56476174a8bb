import React, { useState, useRef, useEffect } from 'react';
import { 
  Mi<PERSON>, 
  Mic<PERSON><PERSON>, 
  Square, 
  Play, 
  Pause, 
  Volume2, 
  Settings, 
  Download,
  Trash2,
  RotateCcw,
  Zap,
  Activity,
  FileText
} from 'lucide-react';

interface EnhancedAudioCaptureProps {
  onTranscriptionUpdate: (text: string) => void;
  isRecording: boolean;
  setIsRecording: (recording: boolean) => void;
}

const EnhancedAudioCapture: React.FC<EnhancedAudioCaptureProps> = ({ 
  onTranscriptionUpdate, 
  isRecording, 
  setIsRecording 
}) => {
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [selectedMicrophone, setSelectedMicrophone] = useState('default');
  const [volumeLevel, setVolumeLevel] = useState(75);
  const [transcriptionAccuracy, setTranscriptionAccuracy] = useState(0);
  const [transcriptionText, setTranscriptionText] = useState('');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // Simulate audio level animation
      const animateAudioLevel = () => {
        setAudioLevel(Math.random() * 100);
        animationRef.current = requestAnimationFrame(animateAudioLevel);
      };
      animateAudioLevel();

      // Simulate transcription updates with accuracy
      const transcriptionInterval = setInterval(() => {
        const sampleTexts = [
          "Let's discuss our quarterly goals and objectives for the upcoming period",
          "The project timeline needs adjustment based on recent feedback from stakeholders",
          "What are the main challenges we face in implementation and deployment?",
          "How can we improve team collaboration and communication effectiveness?",
          "Let's review the budget allocation for next quarter and resource planning"
        ];
        const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
        setTranscriptionText(prev => prev + ' ' + randomText);
        onTranscriptionUpdate(transcriptionText + ' ' + randomText);
        
        // Simulate improving accuracy over time
        setTranscriptionAccuracy(prev => Math.min(98, prev + Math.random() * 5));
      }, 3000);

      return () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
        if (animationRef.current) cancelAnimationFrame(animationRef.current);
        clearInterval(transcriptionInterval);
      };
    } else {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (animationRef.current) cancelAnimationFrame(animationRef.current);
      if (!isRecording) {
        setRecordingTime(0);
        setAudioLevel(0);
        setIsPaused(false);
        setTranscriptionAccuracy(0);
      }
    }
  }, [isRecording, isPaused, onTranscriptionUpdate, transcriptionText]);

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      setTranscriptionText('');
      setTranscriptionAccuracy(0);
    }
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  const clearTranscription = () => {
    setTranscriptionText('');
    onTranscriptionUpdate('');
  };

  const exportTranscript = () => {
    const blob = new Blob([transcriptionText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${Date.now()}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-2xl font-semibold text-white mb-2 flex items-center">
          <Activity className="h-6 w-6 mr-3 text-purple-400" />
          Real-time Audio Recording and Transcription
        </h3>
        <div className="flex items-center space-x-4">
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            isRecording 
              ? isPaused 
                ? 'bg-yellow-600 text-yellow-100' 
                : 'bg-red-600 text-red-100'
              : 'bg-gray-600 text-gray-100'
          }`}>
            {isRecording ? (isPaused ? 'Paused' : 'Recording') : 'Stopped'}
          </div>
          {isRecording && (
            <div className="text-white font-mono text-lg">
              {formatTime(recordingTime)}
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recording Controls */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Mic className="h-5 w-5 mr-2 text-blue-400" />
            Recording Controls
          </h4>

          {/* Main Recording Button */}
          <div className="text-center mb-6">
            <button
              onClick={toggleRecording}
              className={`w-24 h-24 rounded-full flex items-center justify-center transition-all transform hover:scale-105 relative overflow-hidden ${
                isRecording 
                  ? 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800' 
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
              }`}
            >
              {isRecording && (
                <div className="absolute inset-0 rounded-full bg-red-400 animate-ping opacity-20"></div>
              )}
              
              {isRecording ? (
                <Square className="h-10 w-10 text-white" />
              ) : (
                <Mic className="h-10 w-10 text-white" />
              )}
            </button>
          </div>

          {/* Control Buttons */}
          <div className="flex justify-center space-x-4 mb-6">
            {isRecording && (
              <button
                onClick={togglePause}
                className="p-3 bg-yellow-600 hover:bg-yellow-700 rounded-full transition-all transform hover:scale-105"
              >
                {isPaused ? (
                  <Play className="h-5 w-5 text-white" />
                ) : (
                  <Pause className="h-5 w-5 text-white" />
                )}
              </button>
            )}
            
            <button
              onClick={clearTranscription}
              className="p-3 bg-red-600 hover:bg-red-700 rounded-full transition-all transform hover:scale-105"
            >
              <Trash2 className="h-5 w-5 text-white" />
            </button>
            
            <button
              onClick={exportTranscript}
              disabled={!transcriptionText}
              className="p-3 bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-full transition-all transform hover:scale-105"
            >
              <Download className="h-5 w-5 text-white" />
            </button>
          </div>

          {/* Audio Waveform */}
          {isRecording && (
            <div className="mb-6">
              <h5 className="text-white font-medium mb-3">Audio Waveform</h5>
              <div className="flex items-center justify-center space-x-1 h-16 bg-black/40 rounded-lg p-4">
                {[...Array(12)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-2 rounded-full transition-all duration-150 ${
                      isPaused ? 'bg-yellow-400' : 'bg-purple-400'
                    }`}
                    style={{
                      height: isPaused ? '20%' : `${Math.max(20, (audioLevel + i * 10) % 100)}%`,
                      animationDelay: `${i * 0.1}s`
                    }}
                  ></div>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-3">
            <button className="flex items-center justify-center space-x-2 p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              <Zap className="h-4 w-4" />
              <span>Generate Questions</span>
            </button>
            <button className="flex items-center justify-center space-x-2 p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
              <FileText className="h-4 w-4" />
              <span>Export Transcript</span>
            </button>
          </div>
        </div>

        {/* Microphone Settings */}
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center">
            <Settings className="h-5 w-5 mr-2 text-green-400" />
            Microphone Settings
          </h4>

          <div className="space-y-6">
            {/* Microphone Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select Microphone
              </label>
              <select 
                value={selectedMicrophone}
                onChange={(e) => setSelectedMicrophone(e.target.value)}
                className="w-full p-3 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="default">Default Microphone</option>
                <option value="usb">USB Microphone</option>
                <option value="bluetooth">Bluetooth Headset</option>
                <option value="internal">Internal Microphone</option>
              </select>
            </div>

            {/* Volume Level */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Volume Level
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={volumeLevel}
                  onChange={(e) => setVolumeLevel(Number(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-sm text-gray-400">
                  <span>0%</span>
                  <span className="text-white font-medium">{volumeLevel}%</span>
                  <span>100%</span>
                </div>
              </div>
            </div>

            {/* Transcription Accuracy */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Transcription Accuracy
              </label>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">{Math.round(transcriptionAccuracy)}%</span>
                  <span className="text-xs text-gray-400">Real-time accuracy</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      transcriptionAccuracy > 90 ? 'bg-green-500' :
                      transcriptionAccuracy > 70 ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}
                    style={{ width: `${transcriptionAccuracy}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Audio Level Meter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Input Level Monitor
              </label>
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-400">
                  <span>Input Level</span>
                  <span>{Math.round(audioLevel)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      audioLevel > 80 ? 'bg-red-500' :
                      audioLevel > 50 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${audioLevel}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Advanced Settings */}
            <div className="pt-4 border-t border-gray-600">
              <h5 className="text-white font-medium mb-3">Advanced Settings</h5>
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" defaultChecked />
                  <span className="text-gray-300 text-sm">Noise Cancellation</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" defaultChecked />
                  <span className="text-gray-300 text-sm">Auto-gain Control</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input type="checkbox" className="w-4 h-4 text-purple-600 rounded" />
                  <span className="text-gray-300 text-sm">Echo Cancellation</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Transcription Display */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-xl font-semibold text-white flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-400" />
            Real-time Transcription
          </h4>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isRecording ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
            <span className="text-sm text-gray-300">{isRecording ? 'Live' : 'Stopped'}</span>
          </div>
        </div>

        <div className="bg-black/40 rounded-lg p-4 min-h-[200px] max-h-[300px] overflow-y-auto border border-purple-500/30">
          {transcriptionText ? (
            <div className="space-y-2">
              <p className="text-gray-200 leading-relaxed">{transcriptionText}</p>
              {isRecording && !isPaused && (
                <div className="flex items-center space-x-1">
                  <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                  <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Mic className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-400 italic">
                  Click the microphone to start recording
                </p>
                <p className="text-gray-500 text-sm mt-1">
                  AI transcription will appear here in real-time
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Transcription Stats */}
        {transcriptionText && (
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Words</p>
              <p className="text-white font-semibold">{transcriptionText.split(' ').length}</p>
            </div>
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Characters</p>
              <p className="text-white font-semibold">{transcriptionText.length}</p>
            </div>
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3 text-center">
              <p className="text-gray-400">Accuracy</p>
              <p className="text-white font-semibold">{Math.round(transcriptionAccuracy)}%</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedAudioCapture;
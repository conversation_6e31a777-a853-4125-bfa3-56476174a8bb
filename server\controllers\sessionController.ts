import { Response } from 'express';
import Session from '../models/Session.js';
import Poll from '../models/Poll.js';
import User from '../models/User.js';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { io } from '../index.js';
import cacheService from '../services/cacheService.js';
import dbOptimization from '../services/databaseOptimization.js';

// Generate unique session code
const generateSessionCode = (): string => {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
};

// Create a new session
export const createSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const {
    title,
    description,
    isPublic = true,
    maxParticipants = 100,
    settings = {}
  } = req.body;

  // Generate unique session code
  let code = generateSessionCode();
  let existingSession = await Session.findOne({ code });

  // Ensure code is unique
  while (existingSession) {
    code = generateSessionCode();
    existingSession = await Session.findOne({ code });
  }

  const defaultSettings = {
    allowAnonymous: false,
    showResults: true,
    allowMultipleAttempts: false,
    autoLaunch: true,
    timerEnabled: true,
    defaultTimer: 30
  };

  const session = await Session.create({
    title,
    description,
    code,
    creator: req.user?._id,
    isPublic,
    maxParticipants,
    settings: { ...defaultSettings, ...settings },
    participants: [req.user?._id], // Creator is automatically a participant
    startTime: new Date()
  });

  await session.populate('creator', 'name email role');

  res.status(201).json({
    success: true,
    message: 'Session created successfully',
    session
  });
});

// Get all sessions (with filtering and pagination)
export const getSessions = asyncHandler(async (req: AuthRequest, res: Response) => {
  const {
    page = 1,
    limit = 10,
    isActive,
    isPublic,
    search,
    creator
  } = req.query;

  const filter: any = {};

  if (isActive !== undefined) filter.isActive = isActive === 'true';
  if (isPublic !== undefined) filter.isPublic = isPublic === 'true';
  if (creator) filter.creator = creator;

  if (search) {
    filter.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { code: { $regex: search, $options: 'i' } }
    ];
  }

  // Users can only see public sessions or sessions they created/participate in
  if (req.user?.role !== 'admin') {
    filter.$or = [
      { isPublic: true },
      { creator: req.user?._id },
      { participants: req.user?._id }
    ];
  }

  // Create cache key based on user and filters
  const cacheKey = `sessions:${req.user?.id}:${JSON.stringify(filter)}:${page}:${limit}`;

  // Try to get from cache first
  const cachedResult = await cacheService.get(cacheKey);
  if (cachedResult) {
    return res.json({
      success: true,
      sessions: cachedResult.sessions,
      pagination: cachedResult.pagination,
      cached: true
    });
  }

  const sessions = await dbOptimization.optimizedFind(Session, filter, {
    populate: [
      { path: 'creator', select: 'name email role' },
      { path: 'participants', select: 'name email' }
    ],
    sort: { createdAt: -1 },
    limit: Number(limit),
    skip: (Number(page) - 1) * Number(limit),
    lean: true
  });

  const total = await Session.countDocuments(filter);

  const result = {
    sessions,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: Math.ceil(total / Number(limit))
    }
  };

  // Cache the result for 5 minutes
  await cacheService.set(cacheKey, result, 300);

  res.json({
    success: true,
    sessions,
    pagination: result.pagination
  });
});

// Get session by ID or code
export const getSessionById = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  // Try cache first
  const cacheKey = `session:${id}`;
  const cachedSession = await cacheService.getCachedSession(id);

  let session;

  if (cachedSession) {
    session = cachedSession;
  } else {
    // Try to find by ID first, then by code
    session = await dbOptimization.optimizedFindOne(Session, { _id: id }, {
      populate: [
        { path: 'creator', select: 'name email role' },
        { path: 'participants', select: 'name email role' },
        {
          path: 'polls',
          populate: {
            path: 'creator',
            select: 'name email'
          }
        }
      ],
      lean: true
    });

    if (!session) {
      session = await dbOptimization.optimizedFindOne(Session, { code: id.toUpperCase() }, {
        populate: [
          { path: 'creator', select: 'name email role' },
          { path: 'participants', select: 'name email role' },
          {
            path: 'polls',
            populate: {
              path: 'creator',
              select: 'name email'
            }
          }
        ],
        lean: true
      });
    }

    if (session) {
      // Cache the session for 10 minutes
      await cacheService.cacheSession(session._id.toString(), session, 600);
    }
  }

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Check access permissions
  const isCreator = session.creator._id.toString() === req.user?._id.toString();
  const isParticipant = session.participants.some(p => p._id.toString() === req.user?._id.toString());
  const isAdmin = req.user?.role === 'admin';

  if (!session.isPublic && !isCreator && !isParticipant && !isAdmin) {
    return res.status(403).json({ message: 'Access denied to this session' });
  }

  res.json({
    success: true,
    session,
    userRole: {
      isCreator,
      isParticipant,
      isAdmin
    }
  });
});

// Join a session
export const joinSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { code } = req.body;

  const session = await Session.findOne({ code: code.toUpperCase() });

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  if (!session.isActive) {
    return res.status(400).json({ message: 'Session is not active' });
  }

  if (session.participants.length >= session.maxParticipants) {
    return res.status(400).json({ message: 'Session is full' });
  }

  // Check if user is already a participant
  if (session.participants.includes(req.user?._id!)) {
    return res.status(400).json({ message: 'You are already a participant in this session' });
  }

  // Add user to session
  session.participants.push(req.user?._id!);

  // Update peak participants if necessary
  if (session.participants.length > session.analytics.peakParticipants) {
    session.analytics.peakParticipants = session.participants.length;
  }

  await session.save();

  // Emit real-time update
  io.to(`session-${session._id}`).emit('participantJoined', {
    user: {
      _id: req.user?._id,
      name: req.user?.name,
      email: req.user?.email
    },
    participantCount: session.participants.length
  });

  await session.populate('creator', 'name email role');
  await session.populate('participants', 'name email role');

  res.json({
    success: true,
    message: 'Successfully joined session',
    session
  });
});

// Leave a session
export const leaveSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Check if user is a participant
  if (!session.participants.includes(req.user?._id!)) {
    return res.status(400).json({ message: 'You are not a participant in this session' });
  }

  // Remove user from session
  session.participants = session.participants.filter(
    p => p.toString() !== req.user?._id.toString()
  );

  await session.save();

  // Emit real-time update
  io.to(`session-${session._id}`).emit('participantLeft', {
    userId: req.user?._id,
    participantCount: session.participants.length
  });

  res.json({
    success: true,
    message: 'Successfully left session'
  });
});

// Update session
export const updateSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;
  const updates = req.body;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can update session
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to update this session' });
  }

  // Prevent updating certain fields
  const allowedUpdates = ['title', 'description', 'isPublic', 'maxParticipants', 'settings'];
  const filteredUpdates: any = {};

  allowedUpdates.forEach(field => {
    if (updates[field] !== undefined) {
      filteredUpdates[field] = updates[field];
    }
  });

  const updatedSession = await Session.findByIdAndUpdate(
    id,
    filteredUpdates,
    { new: true, runValidators: true }
  ).populate('creator', 'name email role')
   .populate('participants', 'name email role');

  // Invalidate cache
  await cacheService.invalidateSession(id);
  await cacheService.invalidatePattern(`sessions:*`); // Invalidate all session lists

  // Update cache with new data
  if (updatedSession) {
    await cacheService.cacheSession(id, updatedSession, 600);
  }

  // Emit real-time update to all participants
  io.to(`session-${session._id}`).emit('sessionUpdated', {
    session: updatedSession
  });

  res.json({
    success: true,
    message: 'Session updated successfully',
    session: updatedSession
  });
});

// Delete session
export const deleteSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can delete session
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to delete this session' });
  }

  // Delete all polls associated with this session
  await Poll.deleteMany({ session: id });

  // Delete the session
  await Session.findByIdAndDelete(id);

  // Invalidate cache
  await cacheService.invalidateSession(id);
  await cacheService.invalidatePattern(`sessions:*`); // Invalidate all session lists

  // Emit real-time update to all participants
  io.to(`session-${session._id}`).emit('sessionDeleted', {
    sessionId: id,
    message: 'Session has been deleted by the host'
  });

  res.json({
    success: true,
    message: 'Session deleted successfully'
  });
});

// Start session
export const startSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can start session
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to start this session' });
  }

  session.isActive = true;
  session.startTime = new Date();
  await session.save();

  // Emit real-time update
  io.to(`session-${session._id}`).emit('sessionStarted', {
    session: {
      _id: session._id,
      title: session.title,
      startTime: session.startTime
    }
  });

  res.json({
    success: true,
    message: 'Session started successfully',
    session
  });
});

// End session
export const endSession = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can end session
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to end this session' });
  }

  session.isActive = false;
  session.endTime = new Date();
  await session.save();

  // End all active polls in this session
  await Poll.updateMany(
    { session: id, isActive: true },
    { isActive: false, endTime: new Date() }
  );

  // Emit real-time update
  io.to(`session-${session._id}`).emit('sessionEnded', {
    session: {
      _id: session._id,
      title: session.title,
      endTime: session.endTime
    }
  });

  res.json({
    success: true,
    message: 'Session ended successfully',
    session
  });
});

// Get session participants
export const getSessionParticipants = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id)
    .populate('participants', 'name email role avatar stats');

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Check access permissions
  const isCreator = session.creator.toString() === req.user?._id.toString();
  const isParticipant = session.participants.some(p => p._id.toString() === req.user?._id.toString());
  const isAdmin = req.user?.role === 'admin';

  if (!session.isPublic && !isCreator && !isParticipant && !isAdmin) {
    return res.status(403).json({ message: 'Access denied to this session' });
  }

  res.json({
    success: true,
    participants: session.participants,
    count: session.participants.length
  });
});

// Get session analytics
export const getSessionAnalytics = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const session = await Session.findById(id).populate('polls');

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can view analytics
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to view analytics for this session' });
  }

  const polls = await Poll.find({ session: id });

  // Calculate analytics
  const totalResponses = polls.reduce((sum, poll) => sum + poll.responses.length, 0);
  const averageResponseTime = polls.length > 0
    ? polls.reduce((sum, poll) => sum + poll.analytics.averageResponseTime, 0) / polls.length
    : 0;
  const averageAccuracy = polls.length > 0
    ? polls.reduce((sum, poll) => sum + poll.analytics.accuracyRate, 0) / polls.length
    : 0;

  const analytics = {
    ...session.analytics.toObject(),
    totalResponses,
    averageResponseTime,
    averageAccuracy,
    pollBreakdown: polls.map(poll => ({
      _id: poll._id,
      title: poll.title,
      responses: poll.responses.length,
      accuracy: poll.analytics.accuracyRate,
      averageTime: poll.analytics.averageResponseTime
    }))
  };

  res.json({
    success: true,
    analytics
  });
});

// Remove participant from session
export const removeParticipant = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id, participantId } = req.params;

  const session = await Session.findById(id);

  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Only creator or admin can remove participants
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to remove participants from this session' });
  }

  // Check if participant exists in session
  if (!session.participants.includes(participantId)) {
    return res.status(400).json({ message: 'User is not a participant in this session' });
  }

  // Remove participant
  session.participants = session.participants.filter(
    p => p.toString() !== participantId
  );

  await session.save();

  // Get participant info for notification
  const participant = await User.findById(participantId).select('name email');

  // Emit real-time update
  io.to(`session-${session._id}`).emit('participantRemoved', {
    participantId,
    participantName: participant?.name,
    participantCount: session.participants.length,
    removedBy: req.user?.name
  });

  res.json({
    success: true,
    message: 'Participant removed successfully'
  });
});

// Get user's sessions
export const getUserSessions = asyncHandler(async (req: AuthRequest, res: Response) => {
  const {
    page = 1,
    limit = 10,
    type = 'all' // 'created', 'joined', 'all'
  } = req.query;

  let filter: any = {};

  switch (type) {
    case 'created':
      filter.creator = req.user?._id;
      break;
    case 'joined':
      filter.participants = req.user?._id;
      filter.creator = { $ne: req.user?._id };
      break;
    default:
      filter.$or = [
        { creator: req.user?._id },
        { participants: req.user?._id }
      ];
  }

  const sessions = await Session.find(filter)
    .populate('creator', 'name email role')
    .populate('participants', 'name email')
    .sort({ updatedAt: -1 })
    .limit(Number(limit))
    .skip((Number(page) - 1) * Number(limit));

  const total = await Session.countDocuments(filter);

  res.json({
    success: true,
    sessions,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: Math.ceil(total / Number(limit))
    }
  });
});
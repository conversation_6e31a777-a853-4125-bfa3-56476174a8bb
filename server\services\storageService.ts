import bcrypt from 'bcryptjs';

// In-memory storage for development when database is not available
export interface InMemoryUser {
  _id: string;
  name: string;
  email: string;
  password: string;
  role: string;
  avatar?: string;
  isVerified: boolean;
  lastLogin?: Date;
  preferences: {
    notifications: boolean;
    theme: string;
    language: string;
  };
  stats: {
    pollsParticipated: number;
    pollsCreated: number;
    correctAnswers: number;
    totalPoints: number;
    currentStreak: number;
    bestStreak: number;
    averageResponseTime: number;
  };
  achievements: string[];
  roleHistory: any[];
  lastRoleSwitch?: Date;
  createdAt: Date;
  updatedAt: Date;
}

class StorageService {
  private inMemoryUsers: Map<string, InMemoryUser> = new Map();
  private userIdCounter = 1;

  isDatabaseAvailable(): boolean {
    // Simple check - in a real app you'd check mongoose connection state
    return process.env.NODE_ENV === 'production';
  }

  async createUser(userData: { name: string; email: string; password: string; role?: string }): Promise<InMemoryUser> {
    const userId = `user_${this.userIdCounter++}`;
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    const user: InMemoryUser = {
      _id: userId,
      name: userData.name,
      email: userData.email.toLowerCase(),
      password: hashedPassword,
      role: userData.role || 'general',
      isVerified: true,
      preferences: {
        notifications: true,
        theme: 'dark',
        language: 'en'
      },
      stats: {
        pollsParticipated: 0,
        pollsCreated: 0,
        correctAnswers: 0,
        totalPoints: 0,
        currentStreak: 0,
        bestStreak: 0,
        averageResponseTime: 0
      },
      achievements: [],
      roleHistory: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.inMemoryUsers.set(userId, user);
    this.inMemoryUsers.set(userData.email.toLowerCase(), user); // Also store by email for lookup
    return user;
  }

  findUserByEmail(email: string): InMemoryUser | undefined {
    return this.inMemoryUsers.get(email.toLowerCase());
  }

  findUserById(id: string): InMemoryUser | undefined {
    return this.inMemoryUsers.get(id);
  }

  updateUser(user: InMemoryUser): void {
    user.updatedAt = new Date();
    this.inMemoryUsers.set(user._id, user);
    this.inMemoryUsers.set(user.email.toLowerCase(), user);
  }

  async comparePassword(candidatePassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, hashedPassword);
  }

  // Get all users (for debugging)
  getAllUsers(): InMemoryUser[] {
    const users: InMemoryUser[] = [];
    const seenIds = new Set<string>();
    
    for (const [key, user] of this.inMemoryUsers.entries()) {
      if (!seenIds.has(user._id)) {
        seenIds.add(user._id);
        users.push(user);
      }
    }
    
    return users;
  }

  // Clear all users (for testing)
  clearAllUsers(): void {
    this.inMemoryUsers.clear();
    this.userIdCounter = 1;
  }
}

// Export singleton instance
export default new StorageService();

import bcrypt from 'bcryptjs';
import crypto from 'crypto';

// In-memory storage for development when database is not available
export interface InMemoryUser {
  _id: string;
  name: string;
  email: string;
  password: string;
  role: string;
  avatar?: string;
  isVerified: boolean;
  lastLogin?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  preferences: {
    notifications: boolean;
    theme: string;
    language: string;
  };
  stats: {
    pollsParticipated: number;
    pollsCreated: number;
    correctAnswers: number;
    totalPoints: number;
    currentStreak: number;
    bestStreak: number;
    averageResponseTime: number;
  };
  achievements: string[];
  roleHistory: any[];
  lastRoleSwitch?: Date;
  createdAt: Date;
  updatedAt: Date;
}

class StorageService {
  private inMemoryUsers: Map<string, InMemoryUser> = new Map();
  private userIdCounter = 1;

  isDatabaseAvailable(): boolean {
    // Simple check - in a real app you'd check mongoose connection state
    return process.env.NODE_ENV === 'production';
  }

  async createUser(userData: { name: string; email: string; password: string; role?: string }): Promise<InMemoryUser> {
    const normalizedEmail = this.normalizeEmail(userData.email);

    // Check if user already exists with this email (case-insensitive)
    if (this.findUserByEmail(normalizedEmail)) {
      throw new Error('User already exists with this email address');
    }

    const userId = `user_${this.userIdCounter++}`;
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    const user: InMemoryUser = {
      _id: userId,
      name: userData.name.trim(),
      email: normalizedEmail,
      password: hashedPassword,
      role: userData.role || 'general',
      isVerified: true,
      preferences: {
        notifications: true,
        theme: 'dark',
        language: 'en'
      },
      stats: {
        pollsParticipated: 0,
        pollsCreated: 0,
        correctAnswers: 0,
        totalPoints: 0,
        currentStreak: 0,
        bestStreak: 0,
        averageResponseTime: 0
      },
      achievements: [],
      roleHistory: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.inMemoryUsers.set(userId, user);
    this.inMemoryUsers.set(normalizedEmail, user); // Store by normalized email for lookup
    return user;
  }

  normalizeEmail(email: string): string {
    return email.toLowerCase().trim();
  }

  findUserByEmail(email: string): InMemoryUser | undefined {
    const normalizedEmail = this.normalizeEmail(email);
    return this.inMemoryUsers.get(normalizedEmail);
  }

  // Check if email exists (case-insensitive)
  emailExists(email: string): boolean {
    const normalizedEmail = this.normalizeEmail(email);
    return this.inMemoryUsers.has(normalizedEmail);
  }

  findUserById(id: string): InMemoryUser | undefined {
    return this.inMemoryUsers.get(id);
  }

  updateUser(user: InMemoryUser): void {
    user.updatedAt = new Date();
    const normalizedEmail = this.normalizeEmail(user.email);
    this.inMemoryUsers.set(user._id, user);
    this.inMemoryUsers.set(normalizedEmail, user);
  }

  async comparePassword(candidatePassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, hashedPassword);
  }

  // Generate password reset token for in-memory user
  generatePasswordResetToken(user: InMemoryUser): string {
    const resetToken = crypto.randomBytes(32).toString('hex');

    // Hash the token and set to passwordResetToken field
    user.passwordResetToken = crypto.createHash('sha256').update(resetToken).digest('hex');

    // Set expire time (10 minutes)
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000);

    // Update user in storage
    this.updateUser(user);

    // Return the unhashed token
    return resetToken;
  }

  // Find user by password reset token
  findUserByResetToken(token: string): InMemoryUser | undefined {
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    for (const user of this.inMemoryUsers.values()) {
      if (user.passwordResetToken === hashedToken &&
          user.passwordResetExpires &&
          user.passwordResetExpires > new Date()) {
        return user;
      }
    }
    return undefined;
  }

  // Reset user password
  async resetPassword(user: InMemoryUser, newPassword: string): Promise<void> {
    user.password = await bcrypt.hash(newPassword, 12);
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    this.updateUser(user);
  }

  // Get all users (for debugging)
  getAllUsers(): InMemoryUser[] {
    const users: InMemoryUser[] = [];
    const seenIds = new Set<string>();

    for (const [key, user] of this.inMemoryUsers.entries()) {
      if (!seenIds.has(user._id)) {
        seenIds.add(user._id);
        users.push(user);
      }
    }

    return users;
  }

  // Clear all users (for testing)
  clearAllUsers(): void {
    this.inMemoryUsers.clear();
    this.userIdCounter = 1;
  }
}

// Export singleton instance
export default new StorageService();

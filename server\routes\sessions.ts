import express from 'express';
import { body, param, query } from 'express-validator';
import {
  createSession,
  getSessions,
  getSessionById,
  joinSession,
  leaveSession,
  updateSession,
  deleteSession,
  startSession,
  endSession,
  getSessionParticipants,
  getSessionAnalytics,
  removeParticipant,
  getUserSessions
} from '../controllers/sessionController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';

const router = express.Router();

// Validation rules
const createSessionValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('maxParticipants')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Max participants must be between 1 and 1000'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),
  body('settings.allowAnonymous')
    .optional()
    .isBoolean()
    .withMessage('allowAnonymous must be a boolean'),
  body('settings.showResults')
    .optional()
    .isBoolean()
    .withMessage('showResults must be a boolean'),
  body('settings.allowMultipleAttempts')
    .optional()
    .isBoolean()
    .withMessage('allowMultipleAttempts must be a boolean'),
  body('settings.autoLaunch')
    .optional()
    .isBoolean()
    .withMessage('autoLaunch must be a boolean'),
  body('settings.timerEnabled')
    .optional()
    .isBoolean()
    .withMessage('timerEnabled must be a boolean'),
  body('settings.defaultTimer')
    .optional()
    .isInt({ min: 5, max: 300 })
    .withMessage('defaultTimer must be between 5 and 300 seconds')
];

const joinSessionValidation = [
  body('code')
    .trim()
    .isLength({ min: 6, max: 6 })
    .withMessage('Session code must be exactly 6 characters')
    .isAlphanumeric()
    .withMessage('Session code must contain only letters and numbers')
];

const updateSessionValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('maxParticipants')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Max participants must be between 1 and 1000'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

// Routes

// Create a new session
router.post('/',
  authenticate,
  authorize(['host', 'admin']),
  createSessionValidation,
  validate,
  createSession
);

// Get all sessions (with filtering and pagination)
router.get('/',
  authenticate,
  paginationValidation,
  validate,
  getSessions
);

// Get user's sessions
router.get('/my-sessions',
  authenticate,
  paginationValidation,
  validate,
  getUserSessions
);

// Join a session
router.post('/join',
  authenticate,
  joinSessionValidation,
  validate,
  joinSession
);

// Get session by ID or code
router.get('/:id',
  authenticate,
  param('id').notEmpty().withMessage('Session ID is required'),
  validate,
  getSessionById
);

// Update session
router.put('/:id',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  updateSessionValidation,
  validate,
  updateSession
);

// Delete session
router.delete('/:id',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  deleteSession
);

// Start session
router.post('/:id/start',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  startSession
);

// End session
router.post('/:id/end',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  endSession
);

// Leave session
router.post('/:id/leave',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  leaveSession
);

// Get session participants
router.get('/:id/participants',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  getSessionParticipants
);

// Get session analytics
router.get('/:id/analytics',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  validate,
  getSessionAnalytics
);

// Remove participant from session
router.delete('/:id/participants/:participantId',
  authenticate,
  param('id').isMongoId().withMessage('Invalid session ID'),
  param('participantId').isMongoId().withMessage('Invalid participant ID'),
  validate,
  removeParticipant
);

export default router;
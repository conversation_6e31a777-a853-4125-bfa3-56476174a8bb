import express from 'express';
import {
  getSessionAnalytics,
  getUserAnalytics,
  getPollAnalytics,
  getLeaderboard,
  exportAnalytics
} from '../controllers/analyticsController.js';
import { authenticate, authorize } from '../middleware/auth.js';

const router = express.Router();

// Get session analytics
router.get('/session/:sessionId', authenticate, authorize('host', 'admin'), getSessionAnalytics);

// Get user analytics
router.get('/user/:userId', authenticate, getUserAnalytics);

// Get poll analytics
router.get('/poll/:pollId', authenticate, authorize('host', 'admin'), getPollAnalytics);

// Get leaderboard
router.get('/leaderboard/:sessionId', authenticate, getLeaderboard);

// Export analytics data
router.get('/export/:sessionId', authenticate, authorize('host', 'admin'), exportAnalytics);

export default router;
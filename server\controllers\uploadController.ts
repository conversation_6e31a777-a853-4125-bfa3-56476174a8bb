import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { deleteFile, getFileUrl } from '../middleware/upload.js';
import User from '../models/User.js';
import Session from '../models/Session.js';
import Poll from '../models/Poll.js';
import path from 'path';

// Upload user avatar
export const uploadUserAvatar = asyncHandler(async (req: AuthRequest, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  const user = await User.findById(req.user?._id);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Delete old avatar if exists
  if (user.avatar) {
    const oldAvatarPath = path.join(process.cwd(), user.avatar);
    try {
      await deleteFile(oldAvatarPath);
    } catch (error) {
      console.error('Error deleting old avatar:', error);
    }
  }

  // Update user avatar path
  const avatarUrl = getFileUrl(req.file.path);
  user.avatar = req.file.path;
  await user.save();

  res.json({
    success: true,
    message: 'Avatar uploaded successfully',
    avatarUrl,
    user: {
      _id: user._id,
      name: user.name,
      email: user.email,
      avatar: avatarUrl
    }
  });
});

// Upload session materials
export const uploadSessionMaterials = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { sessionId } = req.params;
  
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'No files uploaded'
    });
  }

  const session = await Session.findById(sessionId);
  if (!session) {
    return res.status(404).json({ message: 'Session not found' });
  }

  // Check if user has permission to upload materials
  if (session.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to upload materials to this session' });
  }

  const uploadedFiles = req.files.map(file => ({
    originalName: file.originalname,
    filename: file.filename,
    path: file.path,
    size: file.size,
    mimetype: file.mimetype,
    url: getFileUrl(file.path),
    uploadedAt: new Date(),
    uploadedBy: req.user?._id
  }));

  // In a real application, you might want to store file metadata in the database
  // For now, we'll just return the file information

  res.json({
    success: true,
    message: `${uploadedFiles.length} file(s) uploaded successfully`,
    files: uploadedFiles
  });
});

// Upload poll attachments
export const uploadPollAttachments = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { pollId } = req.params;
  
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'No files uploaded'
    });
  }

  const poll = await Poll.findById(pollId);
  if (!poll) {
    return res.status(404).json({ message: 'Poll not found' });
  }

  // Check if user has permission to upload attachments
  if (poll.creator.toString() !== req.user?._id.toString() && req.user?.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to upload attachments to this poll' });
  }

  const uploadedFiles = req.files.map(file => ({
    originalName: file.originalname,
    filename: file.filename,
    path: file.path,
    size: file.size,
    mimetype: file.mimetype,
    url: getFileUrl(file.path),
    uploadedAt: new Date(),
    uploadedBy: req.user?._id
  }));

  res.json({
    success: true,
    message: `${uploadedFiles.length} attachment(s) uploaded successfully`,
    files: uploadedFiles
  });
});

// Delete uploaded file
export const deleteUploadedFile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { filename } = req.params;
  const { type } = req.query; // 'avatar', 'session', 'poll'

  let filePath: string;
  let authorized = false;

  switch (type) {
    case 'avatar':
      // Users can only delete their own avatar
      const user = await User.findById(req.user?._id);
      if (user && user.avatar && user.avatar.includes(filename)) {
        filePath = user.avatar;
        authorized = true;
        // Clear avatar from user record
        user.avatar = undefined;
        await user.save();
      }
      break;
    
    case 'session':
      // Only session creators or admins can delete session materials
      // This would require storing file metadata in database to check ownership
      if (req.user?.role === 'admin') {
        filePath = path.join('uploads/sessions', filename);
        authorized = true;
      }
      break;
    
    case 'poll':
      // Only poll creators or admins can delete poll attachments
      if (req.user?.role === 'admin') {
        filePath = path.join('uploads/polls', filename);
        authorized = true;
      }
      break;
    
    default:
      return res.status(400).json({
        success: false,
        message: 'Invalid file type specified'
      });
  }

  if (!authorized) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to delete this file'
    });
  }

  try {
    await deleteFile(path.join(process.cwd(), filePath!));
    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting file'
    });
  }
});

// Get file info
export const getFileInfo = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { filename } = req.params;
  const { type } = req.query;

  // This would typically query a database for file metadata
  // For now, we'll return basic info based on the file system

  let filePath: string;
  switch (type) {
    case 'avatar':
      filePath = path.join('uploads/avatars', filename);
      break;
    case 'session':
      filePath = path.join('uploads/sessions', filename);
      break;
    case 'poll':
      filePath = path.join('uploads/polls', filename);
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'Invalid file type specified'
      });
  }

  const fullPath = path.join(process.cwd(), filePath);
  
  try {
    const fs = await import('fs');
    const stats = fs.statSync(fullPath);
    
    res.json({
      success: true,
      file: {
        filename,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        url: getFileUrl(filePath)
      }
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      message: 'File not found'
    });
  }
});

// List user's uploaded files
export const getUserFiles = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { type } = req.query; // 'avatar', 'session', 'poll', 'all'
  
  // This would typically query a database for user's files
  // For now, we'll return the user's avatar if it exists
  
  const user = await User.findById(req.user?._id);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  const files: any[] = [];

  if ((type === 'avatar' || type === 'all') && user.avatar) {
    files.push({
      type: 'avatar',
      filename: path.basename(user.avatar),
      url: getFileUrl(user.avatar),
      uploadedAt: user.updatedAt
    });
  }

  res.json({
    success: true,
    files,
    count: files.length
  });
});

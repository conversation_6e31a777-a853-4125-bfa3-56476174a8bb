import { Response } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';

const generateToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'fallback-secret', {
    expiresIn: process.env.JWT_EXPIRE || '7d'
  });
};

const generateRefreshToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret', {
    expiresIn: '30d'
  });
};

export const register = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { name, email, password } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return res.status(400).json({ message: 'User already exists with this email' });
  }

  // Create user
  const user = await User.create({
    name,
    email,
    password,
    role: 'general'
  });

  // Generate tokens
  const token = generateToken(user._id.toString());
  const refreshToken = generateRefreshToken(user._id.toString());

  // Remove password from response
  const userResponse = user.toObject();
  delete userResponse.password;

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    user: userResponse,
    token,
    refreshToken
  });
});

export const login = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { email, password } = req.body;

  // Find user and include password for comparison
  const user = await User.findOne({ email }).select('+password');
  if (!user) {
    return res.status(401).json({ message: 'Invalid email or password' });
  }

  // Check password
  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    return res.status(401).json({ message: 'Invalid email or password' });
  }

  // Update last login
  user.lastLogin = new Date();
  await user.save();

  // Generate tokens
  const token = generateToken(user._id.toString());
  const refreshToken = generateRefreshToken(user._id.toString());

  // Remove password from response
  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Login successful',
    user: userResponse,
    token,
    refreshToken
  });
});

export const getProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await User.findById(req.user?._id).select('-password');
  
  res.json({
    success: true,
    user
  });
});

export const updateProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { name, email, preferences } = req.body;
  
  const user = await User.findById(req.user?._id);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if email is being changed and if it's already taken
  if (email && email !== user.email) {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'Email already in use' });
    }
    user.email = email;
  }

  if (name) user.name = name;
  if (preferences) user.preferences = { ...user.preferences, ...preferences };

  await user.save();

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: userResponse
  });
});

export const switchRole = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { newRole } = req.body;
  const user = await User.findById(req.user?._id);
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if user can switch roles (rate limiting)
  if (user.lastRoleSwitch) {
    const timeSinceLastSwitch = Date.now() - user.lastRoleSwitch.getTime();
    const oneDay = 24 * 60 * 60 * 1000;
    
    if (timeSinceLastSwitch < oneDay) {
      return res.status(400).json({ 
        message: 'You can only switch roles once per day',
        nextSwitchAllowed: new Date(user.lastRoleSwitch.getTime() + oneDay)
      });
    }
  }

  // Validate role transition
  const allowedTransitions: { [key: string]: string[] } = {
    general: ['participant', 'host'],
    participant: ['host'],
    host: ['participant'],
    admin: ['participant', 'host']
  };

  if (!allowedTransitions[user.role]?.includes(newRole)) {
    return res.status(400).json({ 
      message: `Cannot switch from ${user.role} to ${newRole}` 
    });
  }

  // For host role, require additional verification (simplified for demo)
  if (newRole === 'host' && user.role !== 'host') {
    // In production, this would involve admin approval
    // For demo, we'll auto-approve
  }

  // Record role change
  user.roleHistory.push({
    from: user.role,
    to: newRole,
    date: new Date(),
    reason: 'User initiated role switch'
  });

  user.role = newRole;
  user.lastRoleSwitch = new Date();
  await user.save();

  // Generate new token with updated role
  const token = generateToken(user._id.toString());

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: `Successfully switched to ${newRole} role`,
    user: userResponse,
    token
  });
});

export const requestHostRole = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { reason } = req.body;
  const user = await User.findById(req.user?._id);
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // For demo purposes, auto-approve host role requests
  // In production, this would create a request for admin approval
  
  user.roleHistory.push({
    from: user.role,
    to: 'host',
    date: new Date(),
    reason: reason || 'Host role requested',
    approvedBy: 'system'
  });

  user.role = 'host';
  user.lastRoleSwitch = new Date();
  await user.save();

  const token = generateToken(user._id.toString());

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Host role approved! You now have host privileges.',
    user: userResponse,
    token
  });
});

export const refreshToken = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    return res.status(401).json({ message: 'Refresh token required' });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret') as { userId: string };
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    const newToken = generateToken(user._id.toString());
    const newRefreshToken = generateRefreshToken(user._id.toString());

    res.json({
      success: true,
      token: newToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    res.status(401).json({ message: 'Invalid refresh token' });
  }
});

export const logout = asyncHandler(async (req: AuthRequest, res: Response) => {
  // In a production app, you might want to blacklist the token
  // For now, we'll just send a success response
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});
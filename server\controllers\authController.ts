import { Response } from 'express';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import User from '../models/User.js';
import { AuthRequest } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import storageService from '../services/storageService.js';
import emailService from '../services/emailService.js';

const generateToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'fallback-secret', {
    expiresIn: process.env.JWT_EXPIRE || '7d'
  });
};

const generateRefreshToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret', {
    expiresIn: '30d'
  });
};



export const register = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { name, email, password } = req.body;

  let user: any;
  let userResponse: any;

  // Normalize email for consistent checking
  const normalizedEmail = email.toLowerCase().trim();

  if (storageService.isDatabaseAvailable()) {
    // Database mode
    try {
      // Check if user already exists (case-insensitive)
      const existingUser = await User.findOne({
        email: { $regex: new RegExp(`^${normalizedEmail}$`, 'i') }
      });
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'An account with this email address already exists. Please use a different email or try logging in.'
        });
      }

      // Create user
      user = await User.create({
        name: name.trim(),
        email: normalizedEmail,
        password,
        role: 'general'
      });

      // Remove password from response
      userResponse = user.toObject();
      delete userResponse.password;
    } catch (error: any) {
      console.log('Database error, falling back to in-memory storage:', error.message);
      // Fall back to in-memory storage
      if (storageService.emailExists(normalizedEmail)) {
        return res.status(409).json({
          success: false,
          message: 'An account with this email address already exists. Please use a different email or try logging in.'
        });
      }

      try {
        user = await storageService.createUser({ name, email: normalizedEmail, password, role: 'general' });
        userResponse = { ...user };
        delete userResponse.password;
      } catch (storageError: any) {
        return res.status(409).json({
          success: false,
          message: storageError.message || 'An account with this email address already exists.'
        });
      }
    }
  } else {
    // In-memory mode for development
    if (storageService.emailExists(normalizedEmail)) {
      return res.status(409).json({
        success: false,
        message: 'An account with this email address already exists. Please use a different email or try logging in.'
      });
    }

    try {
      user = await storageService.createUser({ name, email: normalizedEmail, password, role: 'general' });
      userResponse = { ...user };
      delete userResponse.password;
    } catch (storageError: any) {
      return res.status(409).json({
        success: false,
        message: storageError.message || 'An account with this email address already exists.'
      });
    }
  }

  // Generate tokens
  const token = generateToken(user._id.toString());
  const refreshToken = generateRefreshToken(user._id.toString());

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    user: userResponse,
    token,
    refreshToken
  });
});

export const login = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { email, password } = req.body;

  let user: any;
  let userResponse: any;

  // Normalize email for consistent lookup
  const normalizedEmail = email.toLowerCase().trim();

  if (storageService.isDatabaseAvailable()) {
    // Database mode
    try {
      // Find user and include password for comparison (case-insensitive)
      user = await User.findOne({
        email: { $regex: new RegExp(`^${normalizedEmail}$`, 'i') }
      }).select('+password');
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Check password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Remove password from response
      userResponse = user.toObject();
      delete userResponse.password;
    } catch (error) {
      console.log('Database error, falling back to in-memory storage');
      // Fall back to in-memory storage
      user = storageService.findUserByEmail(normalizedEmail);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Check password
      const isPasswordValid = await storageService.comparePassword(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Update last login
      user.lastLogin = new Date();
      storageService.updateUser(user);

      userResponse = { ...user };
      delete userResponse.password;
    }
  } else {
    // In-memory mode for development
    user = storageService.findUserByEmail(normalizedEmail);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check password
    const isPasswordValid = await storageService.comparePassword(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last login
    user.lastLogin = new Date();
    storageService.updateUser(user);

    userResponse = { ...user };
    delete userResponse.password;
  }

  // Generate tokens
  const token = generateToken(user._id.toString());
  const refreshToken = generateRefreshToken(user._id.toString());

  res.json({
    success: true,
    message: 'Login successful',
    user: userResponse,
    token,
    refreshToken
  });
});

export const getProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  let user: any;

  if (storageService.isDatabaseAvailable()) {
    try {
      user = await User.findById(req.user?._id).select('-password');
    } catch (error) {
      // Fall back to in-memory storage
      user = storageService.findUserById(req.user?._id || '');
      if (user) {
        user = { ...user };
        delete user.password;
      }
    }
  } else {
    // In-memory mode for development
    user = storageService.findUserById(req.user?._id || '');
    if (user) {
      user = { ...user };
      delete user.password;
    }
  }

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  res.json({
    success: true,
    user
  });
});

export const updateProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { name, email, preferences } = req.body;
  
  const user = await User.findById(req.user?._id);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if email is being changed and if it's already taken
  if (email && email !== user.email) {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'Email already in use' });
    }
    user.email = email;
  }

  if (name) user.name = name;
  if (preferences) user.preferences = { ...user.preferences, ...preferences };

  await user.save();

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: userResponse
  });
});

export const switchRole = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { newRole } = req.body;
  const user = await User.findById(req.user?._id);
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if user can switch roles (rate limiting)
  if (user.lastRoleSwitch) {
    const timeSinceLastSwitch = Date.now() - user.lastRoleSwitch.getTime();
    const oneDay = 24 * 60 * 60 * 1000;
    
    if (timeSinceLastSwitch < oneDay) {
      return res.status(400).json({ 
        message: 'You can only switch roles once per day',
        nextSwitchAllowed: new Date(user.lastRoleSwitch.getTime() + oneDay)
      });
    }
  }

  // Validate role transition
  const allowedTransitions: { [key: string]: string[] } = {
    general: ['participant', 'host'],
    participant: ['host'],
    host: ['participant'],
    admin: ['participant', 'host']
  };

  if (!allowedTransitions[user.role]?.includes(newRole)) {
    return res.status(400).json({ 
      message: `Cannot switch from ${user.role} to ${newRole}` 
    });
  }

  // For host role, require additional verification (simplified for demo)
  if (newRole === 'host' && user.role !== 'host') {
    // In production, this would involve admin approval
    // For demo, we'll auto-approve
  }

  // Record role change
  user.roleHistory.push({
    from: user.role,
    to: newRole,
    date: new Date(),
    reason: 'User initiated role switch'
  });

  user.role = newRole;
  user.lastRoleSwitch = new Date();
  await user.save();

  // Generate new token with updated role
  const token = generateToken(user._id.toString());

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: `Successfully switched to ${newRole} role`,
    user: userResponse,
    token
  });
});

export const requestHostRole = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { reason } = req.body;
  const user = await User.findById(req.user?._id);
  
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // For demo purposes, auto-approve host role requests
  // In production, this would create a request for admin approval
  
  user.roleHistory.push({
    from: user.role,
    to: 'host',
    date: new Date(),
    reason: reason || 'Host role requested',
    approvedBy: 'system'
  });

  user.role = 'host';
  user.lastRoleSwitch = new Date();
  await user.save();

  const token = generateToken(user._id.toString());

  const userResponse = user.toObject();
  delete userResponse.password;

  res.json({
    success: true,
    message: 'Host role approved! You now have host privileges.',
    user: userResponse,
    token
  });
});

export const refreshToken = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    return res.status(401).json({ message: 'Refresh token required' });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret') as { userId: string };
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    const newToken = generateToken(user._id.toString());
    const newRefreshToken = generateRefreshToken(user._id.toString());

    res.json({
      success: true,
      token: newToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    res.status(401).json({ message: 'Invalid refresh token' });
  }
});

export const logout = asyncHandler(async (req: AuthRequest, res: Response) => {
  // In a production app, you might want to blacklist the token
  // For now, we'll just send a success response
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

export const forgotPassword = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { email } = req.body;

  // Normalize email for consistent lookup
  const normalizedEmail = email.toLowerCase().trim();

  let user: any;
  let resetToken: string;

  if (storageService.isDatabaseAvailable()) {
    // Database mode
    try {
      // Find user by email (case-insensitive)
      user = await User.findOne({
        email: { $regex: new RegExp(`^${normalizedEmail}$`, 'i') }
      });

      if (!user) {
        // Don't reveal whether user exists or not for security
        return res.json({
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent.'
        });
      }

      // Generate reset token
      resetToken = user.generatePasswordResetToken();
      await user.save();
    } catch (error) {
      console.log('Database error, falling back to in-memory storage');
      // Fall back to in-memory storage
      user = storageService.findUserByEmail(normalizedEmail);

      if (!user) {
        return res.json({
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent.'
        });
      }

      resetToken = storageService.generatePasswordResetToken(user);
    }
  } else {
    // In-memory mode for development
    user = storageService.findUserByEmail(normalizedEmail);

    if (!user) {
      return res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }

    resetToken = storageService.generatePasswordResetToken(user);
  }

  // Send password reset email
  try {
    await emailService.sendPasswordResetEmail(user.email, resetToken, user.name);

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  } catch (error) {
    console.error('Failed to send password reset email:', error);

    // Clear the reset token if email failed
    if (storageService.isDatabaseAvailable()) {
      try {
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();
      } catch (dbError) {
        // If database fails, clear from in-memory storage
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        storageService.updateUser(user);
      }
    } else {
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      storageService.updateUser(user);
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send password reset email. Please try again later.'
    });
  }
});

export const resetPassword = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({
      success: false,
      message: 'Token and new password are required.'
    });
  }

  let user: any;

  if (storageService.isDatabaseAvailable()) {
    // Database mode
    try {
      // Hash the token to compare with stored hashed token
      const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

      // Find user with valid reset token
      user = await User.findOne({
        passwordResetToken: hashedToken,
        passwordResetExpires: { $gt: new Date() }
      });

      if (!user) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired password reset token.'
        });
      }

      // Set new password
      user.password = password;
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save();
    } catch (error) {
      console.log('Database error, falling back to in-memory storage');
      // Fall back to in-memory storage
      user = storageService.findUserByResetToken(token);

      if (!user) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired password reset token.'
        });
      }

      await storageService.resetPassword(user, password);
    }
  } else {
    // In-memory mode for development
    user = storageService.findUserByResetToken(token);

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired password reset token.'
      });
    }

    await storageService.resetPassword(user, password);
  }

  // Generate new tokens for automatic login
  const authToken = generateToken(user._id.toString());
  const refreshToken = generateRefreshToken(user._id.toString());

  // Remove password from response
  const userResponse = { ...user };
  delete userResponse.password;
  delete userResponse.passwordResetToken;
  delete userResponse.passwordResetExpires;

  res.json({
    success: true,
    message: 'Password reset successful. You are now logged in.',
    user: userResponse,
    token: authToken,
    refreshToken
  });
});
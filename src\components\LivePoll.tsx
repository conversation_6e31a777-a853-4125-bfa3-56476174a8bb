import React, { useState } from 'react';
import { CheckCircle } from 'lucide-react';

interface Poll {
  id: string;
  question: string;
  options: string[];
  type: string;
}

interface LivePollProps {
  poll: Poll;
  onSubmit: (selectedOption: number) => void;
}

const LivePoll: React.FC<LivePollProps> = ({ poll, onSubmit }) => {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);

  const handleSubmit = () => {
    if (selectedOption !== null && !hasSubmitted) {
      setHasSubmitted(true);
      onSubmit(selectedOption);
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-white mb-4">{poll.question}</h2>
        <p className="text-gray-300 text-sm">Select your answer below:</p>
      </div>

      <div className="space-y-3 mb-6">
        {poll.options.map((option, index) => (
          <button
            key={index}
            onClick={() => setSelectedOption(index)}
            disabled={hasSubmitted}
            className={`w-full p-4 text-left rounded-lg border transition-all ${
              selectedOption === index
                ? 'bg-purple-600 border-purple-500 text-white'
                : 'bg-white/10 border-gray-600 text-gray-300 hover:bg-white/20'
            } ${hasSubmitted ? 'opacity-50 cursor-not-allowed' : 'hover:border-purple-400'}`}
          >
            <div className="flex items-center justify-between">
              <span className="font-medium">
                {String.fromCharCode(65 + index)}. {option}
              </span>
              {selectedOption === index && (
                <CheckCircle className="h-5 w-5 text-white" />
              )}
            </div>
          </button>
        ))}
      </div>

      <div className="flex justify-center">
        <button
          onClick={handleSubmit}
          disabled={selectedOption === null || hasSubmitted}
          className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
        >
          {hasSubmitted ? 'Submitted!' : 'Submit Answer'}
        </button>
      </div>

      {hasSubmitted && (
        <div className="mt-4 text-center">
          <p className="text-green-400 font-medium">
            Thank you for participating! Results will be shown shortly.
          </p>
        </div>
      )}
    </div>
  );
};

export default LivePoll;
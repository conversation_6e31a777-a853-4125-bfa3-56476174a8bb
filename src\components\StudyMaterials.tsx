import React, { useState } from 'react';
import { 
  BookOpen, 
  Download, 
  Eye, 
  Search, 
  Filter, 
  Star,
  Clock,
  FileText,
  Video,
  Headphones,
  Image,
  Link,
  Tag,
  Calendar,
  User
} from 'lucide-react';

interface StudyMaterial {
  id: string;
  title: string;
  description: string;
  type: 'pdf' | 'video' | 'audio' | 'link' | 'image';
  category: string;
  tags: string[];
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration?: string;
  size?: string;
  rating: number;
  downloads: number;
  uploadDate: Date;
  author: string;
  thumbnail?: string;
}

const StudyMaterials: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const materials: StudyMaterial[] = [
    {
      id: '1',
      title: 'React Hooks Complete Guide',
      description: 'Comprehensive guide covering all React hooks with practical examples and best practices.',
      type: 'pdf',
      category: 'React',
      tags: ['hooks', 'useState', 'useEffect', 'custom-hooks'],
      difficulty: 'Intermediate',
      size: '2.4 MB',
      rating: 4.8,
      downloads: 1247,
      uploadDate: new Date('2024-01-15'),
      author: 'Dr. Sarah Johnson'
    },
    {
      id: '2',
      title: 'JavaScript ES6+ Features Explained',
      description: 'Video tutorial series covering modern JavaScript features including arrow functions, destructuring, and async/await.',
      type: 'video',
      category: 'JavaScript',
      tags: ['es6', 'arrow-functions', 'destructuring', 'async-await'],
      difficulty: 'Beginner',
      duration: '2h 15m',
      rating: 4.9,
      downloads: 2156,
      uploadDate: new Date('2024-01-10'),
      author: 'Prof. Michael Chen'
    },
    {
      id: '3',
      title: 'TypeScript Fundamentals Audio Course',
      description: 'Audio-based learning course for TypeScript basics, perfect for learning on the go.',
      type: 'audio',
      category: 'TypeScript',
      tags: ['typescript', 'types', 'interfaces', 'generics'],
      difficulty: 'Beginner',
      duration: '3h 45m',
      rating: 4.6,
      downloads: 892,
      uploadDate: new Date('2024-01-08'),
      author: 'Emma Rodriguez'
    },
    {
      id: '4',
      title: 'Advanced React Patterns',
      description: 'Deep dive into advanced React patterns including render props, higher-order components, and compound components.',
      type: 'pdf',
      category: 'React',
      tags: ['patterns', 'hoc', 'render-props', 'compound-components'],
      difficulty: 'Advanced',
      size: '3.8 MB',
      rating: 4.7,
      downloads: 756,
      uploadDate: new Date('2024-01-05'),
      author: 'Dr. Alex Thompson'
    },
    {
      id: '5',
      title: 'CSS Grid Layout Cheat Sheet',
      description: 'Visual reference guide for CSS Grid properties and common layout patterns.',
      type: 'image',
      category: 'CSS',
      tags: ['css-grid', 'layout', 'responsive', 'cheat-sheet'],
      difficulty: 'Intermediate',
      size: '1.2 MB',
      rating: 4.5,
      downloads: 1834,
      uploadDate: new Date('2024-01-12'),
      author: 'Lisa Park'
    },
    {
      id: '6',
      title: 'MDN Web Docs - JavaScript Reference',
      description: 'Official Mozilla documentation for JavaScript language reference.',
      type: 'link',
      category: 'JavaScript',
      tags: ['reference', 'documentation', 'mdn'],
      difficulty: 'Beginner',
      rating: 4.9,
      downloads: 3421,
      uploadDate: new Date('2024-01-01'),
      author: 'Mozilla Foundation'
    }
  ];

  const categories = ['all', 'React', 'JavaScript', 'TypeScript', 'CSS', 'HTML', 'Node.js'];
  const types = ['all', 'pdf', 'video', 'audio', 'link', 'image'];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <FileText className="h-5 w-5 text-red-400" />;
      case 'video': return <Video className="h-5 w-5 text-blue-400" />;
      case 'audio': return <Headphones className="h-5 w-5 text-green-400" />;
      case 'link': return <Link className="h-5 w-5 text-purple-400" />;
      case 'image': return <Image className="h-5 w-5 text-yellow-400" />;
      default: return <FileText className="h-5 w-5 text-gray-400" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-600/20 text-green-300 border-green-500/30';
      case 'Intermediate': return 'bg-yellow-600/20 text-yellow-300 border-yellow-500/30';
      case 'Advanced': return 'bg-red-600/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-500/30';
    }
  };

  const filteredMaterials = materials
    .filter(material => 
      material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(material => selectedCategory === 'all' || material.category === selectedCategory)
    .filter(material => selectedType === 'all' || material.type === selectedType)
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest': return b.uploadDate.getTime() - a.uploadDate.getTime();
        case 'oldest': return a.uploadDate.getTime() - b.uploadDate.getTime();
        case 'rating': return b.rating - a.rating;
        case 'downloads': return b.downloads - a.downloads;
        case 'title': return a.title.localeCompare(b.title);
        default: return 0;
      }
    });

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/50 to-blue-900/50 backdrop-blur-lg rounded-xl p-6 border border-green-500/30">
        <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <BookOpen className="h-6 w-6 mr-3 text-green-400" />
          Study Materials & Resources
        </h3>
        <p className="text-green-200">Access curated learning materials to enhance your knowledge</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search materials..."
              className="w-full pl-10 pr-4 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>

          {/* Type Filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {types.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type.toUpperCase()}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-black/40 border border-purple-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="rating">Highest Rated</option>
            <option value="downloads">Most Downloaded</option>
            <option value="title">Alphabetical</option>
          </select>
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMaterials.map((material) => (
          <div
            key={material.id}
            className="bg-black/60 backdrop-blur-lg rounded-xl p-6 border border-purple-500/30 hover:bg-black/80 transition-all hover:scale-105"
          >
            {/* Material Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getTypeIcon(material.type)}
                <div>
                  <h4 className="text-white font-semibold text-lg">{material.title}</h4>
                  <p className="text-gray-400 text-sm">{material.category}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded text-xs border ${getDifficultyColor(material.difficulty)}`}>
                {material.difficulty}
              </span>
            </div>

            {/* Description */}
            <p className="text-gray-300 text-sm mb-4 line-clamp-3">{material.description}</p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {material.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded border border-purple-500/30"
                >
                  #{tag}
                </span>
              ))}
              {material.tags.length > 3 && (
                <span className="text-gray-400 text-xs">+{material.tags.length - 3} more</span>
              )}
            </div>

            {/* Rating and Stats */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-1">
                {renderStars(material.rating)}
                <span className="text-gray-300 text-sm ml-2">{material.rating}</span>
              </div>
              <div className="text-gray-400 text-sm">
                {material.downloads.toLocaleString()} downloads
              </div>
            </div>

            {/* Material Info */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Author:</span>
                <span className="text-gray-300">{material.author}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">
                  {material.duration ? 'Duration:' : 'Size:'}
                </span>
                <span className="text-gray-300">
                  {material.duration || material.size}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Uploaded:</span>
                <span className="text-gray-300">{material.uploadDate.toLocaleDateString()}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                <Eye className="h-4 w-4" />
                <span>View</span>
              </button>
              <button className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                <Download className="h-4 w-4" />
                <span>Download</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredMaterials.length === 0 && (
        <div className="bg-black/60 backdrop-blur-lg rounded-xl p-12 border border-purple-500/30 text-center">
          <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-white text-xl font-semibold mb-2">No materials found</h4>
          <p className="text-gray-400">Try adjusting your search criteria or filters.</p>
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4 text-center">
          <FileText className="h-8 w-8 text-blue-400 mx-auto mb-2" />
          <p className="text-white text-lg font-bold">{materials.filter(m => m.type === 'pdf').length}</p>
          <p className="text-blue-300 text-sm">PDF Documents</p>
        </div>

        <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4 text-center">
          <Video className="h-8 w-8 text-green-400 mx-auto mb-2" />
          <p className="text-white text-lg font-bold">{materials.filter(m => m.type === 'video').length}</p>
          <p className="text-green-300 text-sm">Video Tutorials</p>
        </div>

        <div className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4 text-center">
          <Headphones className="h-8 w-8 text-purple-400 mx-auto mb-2" />
          <p className="text-white text-lg font-bold">{materials.filter(m => m.type === 'audio').length}</p>
          <p className="text-purple-300 text-sm">Audio Courses</p>
        </div>

        <div className="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4 text-center">
          <Link className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
          <p className="text-white text-lg font-bold">{materials.filter(m => m.type === 'link').length}</p>
          <p className="text-yellow-300 text-sm">External Links</p>
        </div>
      </div>
    </div>
  );
};

export default StudyMaterials;
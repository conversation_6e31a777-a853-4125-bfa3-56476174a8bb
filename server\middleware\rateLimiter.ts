import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from './auth.js';
import cacheService from '../services/cacheService.js';

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum number of requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: Request) => string; // Custom key generator
}

class RateLimiter {
  private options: RateLimitOptions;

  constructor(options: RateLimitOptions) {
    this.options = {
      message: 'Too many requests, please try again later.',
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (req: Request) => req.ip || 'unknown',
      ...options
    };
  }

  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const key = this.options.keyGenerator!(req);
        const rateLimitKey = `rate_limit:${key}`;
        const windowSeconds = Math.floor(this.options.windowMs / 1000);

        // Get current count
        const currentCount = await cacheService.incrementRateLimit(rateLimitKey, windowSeconds);

        // Set headers
        res.set({
          'X-RateLimit-Limit': this.options.maxRequests.toString(),
          'X-RateLimit-Remaining': Math.max(0, this.options.maxRequests - currentCount).toString(),
          'X-RateLimit-Reset': new Date(Date.now() + this.options.windowMs).toISOString()
        });

        // Check if limit exceeded
        if (currentCount > this.options.maxRequests) {
          return res.status(429).json({
            success: false,
            message: this.options.message,
            retryAfter: Math.ceil(this.options.windowMs / 1000)
          });
        }

        // Override res.end to handle skipSuccessfulRequests and skipFailedRequests
        if (this.options.skipSuccessfulRequests || this.options.skipFailedRequests) {
          const originalEnd = res.end;
          res.end = function(this: Response, ...args: any[]) {
            const shouldSkip = 
              (this.statusCode < 400 && rateLimiter.options.skipSuccessfulRequests) ||
              (this.statusCode >= 400 && rateLimiter.options.skipFailedRequests);

            if (shouldSkip) {
              // Decrement the counter if we should skip this request
              cacheService.incrementRateLimit(rateLimitKey, windowSeconds).then(count => {
                if (count > 1) {
                  // Decrement by setting a lower value (this is a simplified approach)
                  // In a real implementation, you might want to use a more sophisticated method
                }
              });
            }

            originalEnd.apply(this, args);
          };
        }

        const rateLimiter = this;
        next();
      } catch (error) {
        console.error('Rate limiter error:', error);
        // If rate limiting fails, allow the request to proceed
        next();
      }
    };
  }
}

// Predefined rate limiters for common use cases

// General API rate limiter - 100 requests per 15 minutes
export const generalRateLimit = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
  message: 'Too many requests from this IP, please try again later.'
});

// Strict rate limiter for sensitive operations - 5 requests per minute
export const strictRateLimit = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 5,
  message: 'Too many attempts, please wait before trying again.'
});

// Auth rate limiter - 10 login attempts per 15 minutes
export const authRateLimit = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 10,
  message: 'Too many login attempts, please try again later.',
  skipSuccessfulRequests: true, // Don't count successful logins
  keyGenerator: (req: Request) => {
    // Use email if available, otherwise IP
    const email = req.body?.email;
    return email ? `auth:${email}` : `auth:ip:${req.ip}`;
  }
});

// Session creation rate limiter - 10 sessions per hour per user
export const sessionCreationRateLimit = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 10,
  message: 'Too many sessions created, please wait before creating another.',
  keyGenerator: (req: AuthRequest) => {
    return `session_creation:${req.user?.id || req.ip}`;
  }
});

// Poll creation rate limiter - 20 polls per hour per user
export const pollCreationRateLimit = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 20,
  message: 'Too many polls created, please wait before creating another.',
  keyGenerator: (req: AuthRequest) => {
    return `poll_creation:${req.user?.id || req.ip}`;
  }
});

// File upload rate limiter - 50 uploads per hour per user
export const uploadRateLimit = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 50,
  message: 'Too many file uploads, please wait before uploading again.',
  keyGenerator: (req: AuthRequest) => {
    return `upload:${req.user?.id || req.ip}`;
  }
});

// Chat message rate limiter - 100 messages per 5 minutes per user
export const chatRateLimit = new RateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 100,
  message: 'Too many messages sent, please slow down.',
  keyGenerator: (req: AuthRequest) => {
    return `chat:${req.user?.id || req.ip}`;
  }
});

// Password reset rate limiter - 3 attempts per hour per email
export const passwordResetRateLimit = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 3,
  message: 'Too many password reset attempts, please try again later.',
  keyGenerator: (req: Request) => {
    const email = req.body?.email;
    return email ? `password_reset:${email}` : `password_reset:ip:${req.ip}`;
  }
});

// Admin operations rate limiter - 50 requests per 10 minutes
export const adminRateLimit = new RateLimiter({
  windowMs: 10 * 60 * 1000, // 10 minutes
  maxRequests: 50,
  message: 'Too many admin operations, please wait.',
  keyGenerator: (req: AuthRequest) => {
    return `admin:${req.user?.id || req.ip}`;
  }
});

// Export the RateLimiter class for custom implementations
export { RateLimiter };

// Helper function to create custom rate limiters
export const createRateLimit = (options: RateLimitOptions) => {
  return new RateLimiter(options);
};

// Utility function to check rate limit without incrementing
export const checkRateLimit = async (key: string, maxRequests: number): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: Date;
}> => {
  try {
    const currentCount = await cacheService.getRateLimit(`rate_limit:${key}`);
    const remaining = Math.max(0, maxRequests - currentCount);
    
    return {
      allowed: currentCount < maxRequests,
      remaining,
      resetTime: new Date(Date.now() + 15 * 60 * 1000) // Default 15 minutes
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return {
      allowed: true,
      remaining: maxRequests,
      resetTime: new Date(Date.now() + 15 * 60 * 1000)
    };
  }
};

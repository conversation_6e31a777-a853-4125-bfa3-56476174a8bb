# Server Configuration
NODE_ENV=development
PORT=5000

# Database
MONGODB_URI=mongodb://localhost:27017/pollflow

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-change-in-production
JWT_EXPIRE=24h
JWT_REFRESH_EXPIRE=7d

# Client URL
CLIENT_URL=http://localhost:5173

# OpenAI API (for AI features)
OPENAI_API_KEY=your-openai-api-key-here

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:5173
SESSION_SECRET=your-session-secret-here

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# WebRTC Configuration (Optional)
TURN_SERVER_URL=turn:your-turn-server.com:3478
TURN_USERNAME=your-turn-username
TURN_CREDENTIAL=your-turn-credential

# Whisper Service Configuration
WHISPER_SERVICE_URL=http://localhost:8000
WHISPER_MODEL=base

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_INTERVAL=24h
BACKUP_RETENTION_DAYS=30
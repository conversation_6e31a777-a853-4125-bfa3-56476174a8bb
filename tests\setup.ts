import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { Server } from 'socket.io';
import { createServer } from 'http';
import express from 'express';

// Global test variables
declare global {
  var __MONGO_URI__: string;
  var __MONGO_DB_NAME__: string;
  var __MONGOD__: MongoMemoryServer;
}

// Setup MongoDB Memory Server
beforeAll(async () => {
  // Create in-memory MongoDB instance
  const mongod = new MongoMemoryServer();
  await mongod.start();
  
  const uri = mongod.getUri();
  const dbName = mongod.instanceInfo?.dbName || 'test';
  
  global.__MONGOD__ = mongod;
  global.__MONGO_URI__ = uri;
  global.__MONGO_DB_NAME__ = dbName;
  
  // Connect to the in-memory database
  await mongoose.connect(uri, {
    dbName: dbName
  });
});

// Clean up database between tests
beforeEach(async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  // Close mongoose connection
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  // Stop MongoDB Memory Server
  if (global.__MONGOD__) {
    await global.__MONGOD__.stop();
  }
});

// Mock Redis for testing
jest.mock('ioredis', () => {
  const mockRedis = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
    keys: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    ping: jest.fn().mockResolvedValue('PONG'),
    on: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn()
  };
  
  return jest.fn(() => mockRedis);
});

// Mock Winston logger for testing
jest.mock('../server/services/logger.ts', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    http: jest.fn(),
    database: jest.fn(),
    auth: jest.fn(),
    session: jest.fn(),
    performance: jest.fn(),
    security: jest.fn(),
    apiRequest: jest.fn(),
    business: jest.fn()
  },
  logUnhandledError: jest.fn(),
  logUnhandledRejection: jest.fn()
}));

// Mock file system operations
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn(),
  unlinkSync: jest.fn()
}));

// Mock multer for file uploads
jest.mock('multer', () => {
  const multer = jest.fn(() => ({
    single: jest.fn(() => (req: any, res: any, next: any) => {
      req.file = {
        fieldname: 'avatar',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        destination: 'uploads/avatars',
        filename: 'test-123.jpg',
        path: 'uploads/avatars/test-123.jpg',
        size: 1024
      };
      next();
    }),
    array: jest.fn(() => (req: any, res: any, next: any) => {
      req.files = [
        {
          fieldname: 'files',
          originalname: 'test1.jpg',
          encoding: '7bit',
          mimetype: 'image/jpeg',
          destination: 'uploads/files',
          filename: 'test1-123.jpg',
          path: 'uploads/files/test1-123.jpg',
          size: 1024
        }
      ];
      next();
    })
  }));
  
  multer.diskStorage = jest.fn();
  multer.memoryStorage = jest.fn();
  
  return multer;
});

// Test utilities
export const createTestApp = () => {
  const app = express();
  const server = createServer(app);
  const io = new Server(server);
  
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  
  return { app, server, io };
};

export const createTestUser = () => ({
  name: 'Test User',
  email: '<EMAIL>',
  password: 'testPassword123',
  role: 'participant' as const
});

export const createTestSession = () => ({
  title: 'Test Session',
  description: 'Test session description',
  isPublic: true,
  maxParticipants: 50,
  settings: {
    allowAnonymous: false,
    requireApproval: true,
    enableChat: true,
    enablePolls: true
  }
});

export const createTestPoll = () => ({
  question: 'Test poll question?',
  options: [
    { text: 'Option 1', votes: 0 },
    { text: 'Option 2', votes: 0 },
    { text: 'Option 3', votes: 0 }
  ],
  type: 'single-choice' as const,
  allowMultipleVotes: false,
  isAnonymous: true
});

// Helper function to generate JWT token for testing
export const generateTestToken = (userId: string, role: string = 'participant') => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { id: userId, role },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

// Helper function to create authenticated request headers
export const getAuthHeaders = (token: string) => ({
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
});

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
process.env.MONGODB_URI = 'mongodb://localhost:27017/test';
process.env.PORT = '5001';
process.env.CLIENT_URL = 'http://localhost:3000';
process.env.CORS_ORIGIN = 'http://localhost:3000';

// Increase timeout for async operations
jest.setTimeout(30000);

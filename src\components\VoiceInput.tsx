import React, { useState, useRef, useEffect } from 'react';
import { Mi<PERSON>, MicOff, Square, Play, Pause, Volume2 } from 'lucide-react';

interface VoiceInputProps {
  onTranscriptionUpdate: (text: string) => void;
  isRecording: boolean;
  setIsRecording: (recording: boolean) => void;
}

const VoiceInput: React.FC<VoiceInputProps> = ({ 
  onTranscriptionUpdate, 
  isRecording, 
  setIsRecording 
}) => {
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // Simulate audio level animation
      const animateAudioLevel = () => {
        setAudioLevel(Math.random() * 100);
        animationRef.current = requestAnimationFrame(animateAudioLevel);
      };
      animateAudioLevel();

      // Simulate transcription updates
      const transcriptionInterval = setInterval(() => {
        const sampleTexts = [
          "Let's discuss our quarterly goals and objectives",
          "The project timeline needs adjustment based on feedback",
          "What are the main challenges we face in implementation?",
          "How can we improve team collaboration and communication?",
          "Let's review the budget allocation for next quarter"
        ];
        const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
        onTranscriptionUpdate(randomText);
      }, 3000);

      return () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
        if (animationRef.current) cancelAnimationFrame(animationRef.current);
        clearInterval(transcriptionInterval);
      };
    } else {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (animationRef.current) cancelAnimationFrame(animationRef.current);
      if (!isRecording) {
        setRecordingTime(0);
        setAudioLevel(0);
        setIsPaused(false);
      }
    }
  }, [isRecording, isPaused, onTranscriptionUpdate]);

  const toggleRecording = () => {
    setIsRecording(!isRecording);
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-black/60 backdrop-blur-lg rounded-xl p-8 border border-purple-500/30 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-4 right-8 w-2 h-2 bg-purple-400 rounded-full animate-ping"></div>
        <div className="absolute bottom-6 left-12 w-1 h-1 bg-pink-400 rounded-full animate-bounce"></div>
        <div className="absolute top-12 left-20 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
          <Volume2 className="h-5 w-5 mr-2 text-purple-400" />
          AI Voice Capture
        </h3>
        
        <div className="text-center space-y-6">
          {/* Main Recording Button */}
          <div className="relative">
            <button
              onClick={toggleRecording}
              className={`w-24 h-24 rounded-full flex items-center justify-center transition-all transform hover:scale-105 relative overflow-hidden ${
                isRecording 
                  ? 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800' 
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
              }`}
            >
              {/* Pulsing ring for recording state */}
              {isRecording && (
                <div className="absolute inset-0 rounded-full bg-red-400 animate-ping opacity-20"></div>
              )}
              
              {isRecording ? (
                <Square className="h-10 w-10 text-white" />
              ) : (
                <Mic className="h-10 w-10 text-white" />
              )}
            </button>
            
            {/* Recording indicator */}
            {isRecording && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            )}
          </div>

          {/* Control Buttons */}
          {isRecording && (
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={togglePause}
                className="p-3 bg-yellow-600 hover:bg-yellow-700 rounded-full transition-all transform hover:scale-105"
              >
                {isPaused ? (
                  <Play className="h-5 w-5 text-white" />
                ) : (
                  <Pause className="h-5 w-5 text-white" />
                )}
              </button>
            </div>
          )}

          {/* Recording Status */}
          {isRecording && (
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                <span className={`text-sm font-medium ${isPaused ? 'text-yellow-400' : 'text-red-400'}`}>
                  {isPaused ? 'Paused' : 'Recording...'}
                </span>
              </div>
              
              <div className="text-white font-mono text-2xl">
                {formatTime(recordingTime)}
              </div>

              {/* Enhanced Audio Waveform Animation */}
              <div className="flex items-center justify-center space-x-1 h-12">
                {[...Array(7)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-1.5 rounded-full transition-all duration-150 ${
                      isPaused ? 'bg-yellow-400' : 'bg-purple-400'
                    }`}
                    style={{
                      height: isPaused ? '20%' : `${Math.max(20, (audioLevel + i * 15) % 100)}%`,
                      animationDelay: `${i * 0.1}s`
                    }}
                  ></div>
                ))}
              </div>

              {/* Audio Level Meter */}
              <div className="w-full max-w-xs mx-auto">
                <div className="flex justify-between text-xs text-gray-400 mb-1">
                  <span>Audio Level</span>
                  <span>{Math.round(audioLevel)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      audioLevel > 80 ? 'bg-red-500' :
                      audioLevel > 50 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${audioLevel}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {!isRecording && (
            <div className="space-y-4">
              <p className="text-gray-300 text-sm">
                Click the microphone to start AI-powered voice capture
              </p>
              <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">AI Features</h4>
                <div className="text-sm text-gray-300 space-y-1">
                  <p>• Real-time speech-to-text conversion</p>
                  <p>• Automatic question generation</p>
                  <p>• Context-aware poll creation</p>
                  <p>• Multi-language support</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceInput;